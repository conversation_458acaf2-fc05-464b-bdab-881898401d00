'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { usePathname } from 'next/navigation';
import ModernSuperAdminSidebar from '@/components/dashboard/ModernSuperAdminSidebar';
import HeaderProvider from '@/components/headers/HeaderProvider';
import SkipLink from '@/components/accessibility/SkipLink';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';

// Import necessary styles
import '@/styles/dashboard-sidebar.css';
import '@/styles/dashboard-banner.css';
import '@/styles/dashboard-layout.css';
import '@/styles/modern-dashboard.css';
import '@/styles/dashboard-inline-header.css';
import '@/styles/fix-section-header-overlap.css';
import '@/styles/consistent-section-headers.css';
import '@/styles/dashboard-width-adjustment.css';
import '@/styles/fix-header-overlap.css';
import '@/styles/no-gap-header.css';
import '@/styles/fix-super-admin-header-overlap.css';
import '@/styles/super-admin-mobile-sidebar.css';
import '@/styles/fix-header-z-index.css';
import '@/styles/fix-super-admin-banner.css';
import '@/styles/dashboard-banner-spacing.css';
import '@/styles/enhanced-dashboard-layout.css';
import '@/styles/fix-header-banner-overlap.css';
import '@/styles/dashboard-width-90-percent.css';
import '@/styles/super-admin-banner-alignment-fix.css';
import '@/styles/fix-banner-header-overlap.css';
import '@/styles/dashboard.css';
import '@/styles/dashboard-header-fix.css';
import '@/styles/super-admin-user-dashboard-consistency.css';
import '@/styles/super-admin-sidebar-consistency.css';
import '@/styles/dashboard-wrapper-consistency.css';
import '@/styles/fix-super-admin-layout.css';
import '@/styles/fix-dashboard-height.css'; // Import fix for dashboard height
import '@/styles/super-admin-content.css'; // Import content section styles
import '@/styles/fix-super-admin-content-space.css'; // Import fix for content section spacing
import '@/styles/fix-modern-dashboard-height.css'; // Import fix for modern dashboard height
import '@/styles/fix-dashboard-wrapper-height.css'; // Import fix for dashboard wrapper height
import '@/styles/fix-content-table-card.css'; // Import fix for content table card
import '@/styles/fix-dashboard-wrapper-min-height.css'; // Import fix for dashboard wrapper min-height
import '@/styles/override-dashboard-min-height.css'; // Import override for dashboard min-height
import '@/styles/fix-super-admin-content-section-space.css'; // Import comprehensive fix for content section space
import '@/styles/fix-super-admin-content-visibility.css'; // Import fix for content visibility
import '@/styles/fix-super-admin-container.css'; // Import fix for container width and spacing
import '@/styles/modern-content-grid.css'; // Import modern content grid styles
import '@/styles/fix-super-admin-dashboard-complete.css'; // Import comprehensive fix for all dashboard width issues
import '@/styles/fix-enhanced-dashboard-banner.css'; // Import fix for enhanced dashboard banner
import '@/styles/fix-super-admin-dashboard-final.css'; // Import final fix for dashboard width issues
import '@/styles/enhanced-dashboard-banner-styles.css'; // Import styles for enhanced dashboard banner
import '@/styles/enhanced-dashboard-banner-styles-part2.css'; // Import additional styles for enhanced dashboard banner
import '@/styles/super-admin-dashboard-consistency.css'; // Import comprehensive Super Admin Dashboard consistency styles - UPDATED
import '@/styles/fix-super-admin-dashboard-display.css'; // Import fix for Super Admin Dashboard display issues
import '@/styles/fix-super-admin-display-final.css'; // Import final fix for Super Admin Dashboard display issues

export default function ModernSuperAdminDashboardLayout({ children }) {
  const { user } = useAuth();
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Toggle sidebar function
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Toggle mobile sidebar function
  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  // Close mobile sidebar when path changes
  useEffect(() => {
    setIsMobileSidebarOpen(false);
  }, [pathname]);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!isMobileSidebarOpen) return;

      const sidebar = document.querySelector('.modern-sidebar');
      const toggleButton = document.querySelector('.mobile-sidebar-toggle');

      if (sidebar &&
          !sidebar.contains(event.target) &&
          toggleButton &&
          !toggleButton.contains(event.target)) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileSidebarOpen]);

  // Check if viewport is mobile and handle sidebar visibility
  useEffect(() => {
    const checkIfMobile = () => {
      const isMobileView = window.innerWidth < 992;
      setIsMobile(isMobileView);

      // On large screens, sidebar is always visible
      // On mobile, sidebar is hidden by default
      if (!isMobileView && !isSidebarOpen) {
        setIsSidebarOpen(true);
      } else if (isMobileView && isSidebarOpen) {
        setIsSidebarOpen(false);
      }
    };

    // Initial check
    checkIfMobile();

    // Add event listener
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, [isSidebarOpen]);

  return (
    <div className="dashboard-wrapper">
      {/* Skip Link for Accessibility */}
      <SkipLink />

      {/* Use the HeaderProvider component */}
      <HeaderProvider />

      <div className="dashboard-content">
        {/* Modern Super Admin Sidebar - Add open class when sidebar is open */}
        <ModernSuperAdminSidebar
          user={{
            name: user?.name || 'Super Admin',
            email: user?.email || '<EMAIL>',
            profilePictureUrl: user?.profileImage || '/assets/img/default-avatar.png',
            pendingVerifications: 12, // This would come from real data in a production app
          }}
        />

        {/* Mobile Sidebar Toggle Button */}
        <div className="mobile-sidebar-toggle">
          <button
            className="toggle-button"
            onClick={toggleMobileSidebar}
            aria-expanded={isMobileSidebarOpen}
            aria-controls="sidebar"
          >
            <FontAwesomeIcon icon={faBars} className="toggle-icon" />
            <span className="toggle-text">{isMobileSidebarOpen ? 'Hide' : 'Show'} Menu</span>
          </button>
        </div>

        {/* Sidebar Overlay (Mobile) */}
        <div
          className={`sidebar-overlay ${isMobileSidebarOpen ? 'visible' : ''}`}
          onClick={() => setIsMobileSidebarOpen(false)}
        ></div>

        {/* Main Content Area */}
        <main id="main-content" className="main-content-wrapper">
          {/* Ensure children are wrapped in a div with proper styling */}
          <div className="super-admin-content-container">
            {/* Check if children is already wrapped in modern-main-content */}
            {React.Children.map(children, child => {
              // If the child already has the modern-main-content class, return it as is
              if (React.isValidElement(child) &&
                  child.props.className &&
                  child.props.className.includes('modern-main-content')) {
                return child;
              }
              // Otherwise, wrap it in a div with the modern-main-content class
              return (
                <div className="modern-main-content super-admin-dashboard">
                  {child}
                </div>
              );
            })}
          </div>

          {/* Footer that extends across both sidebar and main content */}
          <footer className="dashboard-footer">
            <p>&copy; {new Date().getFullYear()} FindaPro. All rights reserved.</p>
          </footer>
        </main>
      </div>
    </div>
  );
}
