'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { usePathname } from 'next/navigation';
import ModernSuperAdminSidebar from '@/components/dashboard/ModernSuperAdminSidebar';
import HeaderProvider from '@/components/headers/HeaderProvider';
import SkipLink from '@/components/accessibility/SkipLink';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';

// Import only essential styles
import '@/styles/dashboard-sidebar.css';
import '@/styles/dashboard-banner.css';
import '@/styles/dashboard-layout.css';
import '@/styles/modern-dashboard.css';

export default function SimpleSuperAdminLayout({ children }) {
  const { user } = useAuth();
  const pathname = usePathname();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Close mobile sidebar when path changes
  useEffect(() => {
    setIsMobileSidebarOpen(false);
  }, [pathname]);

  return (
    <div className="dashboard-wrapper" style={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Skip Link for Accessibility */}
      <SkipLink />

      {/* Use the HeaderProvider component */}
      <HeaderProvider />

      <div className="dashboard-content" style={{ display: 'flex', flex: 1, position: 'relative', marginTop: '60px' }}>
        {/* Modern Super Admin Sidebar */}
        <ModernSuperAdminSidebar
          user={{
            name: user?.name || 'Super Admin',
            email: user?.email || '<EMAIL>',
            profilePictureUrl: user?.profileImage || '/assets/img/default-avatar.png',
            pendingVerifications: 12,
          }}
        />

        {/* Mobile Sidebar Toggle Button */}
        <div className="mobile-sidebar-toggle" style={{ display: 'none', position: 'fixed', bottom: '20px', right: '20px', zIndex: 1000 }}>
          <button
            className="toggle-button"
            onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
            aria-expanded={isMobileSidebarOpen}
            aria-controls="sidebar"
          >
            <FontAwesomeIcon icon={faBars} className="toggle-icon" />
            <span className="toggle-text">{isMobileSidebarOpen ? 'Hide' : 'Show'} Menu</span>
          </button>
        </div>

        {/* Main Content Area */}
        <main 
          id="main-content" 
          style={{ 
            flex: 1, 
            marginLeft: '280px', 
            width: 'calc(100% - 280px)', 
            marginTop: '70px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          {children}
          
          {/* Footer */}
          <footer style={{ 
            width: '100%', 
            marginTop: 'auto', 
            padding: '1.5rem', 
            backgroundColor: 'white', 
            borderTop: '1px solid #e5e7eb', 
            textAlign: 'center' 
          }}>
            <p>&copy; {new Date().getFullYear()} FindaPro. All rights reserved.</p>
          </footer>
        </main>
      </div>
    </div>
  );
}
