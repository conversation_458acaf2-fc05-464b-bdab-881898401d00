/* Fix Super Admin Dashboard Container
 * This file ensures that the dashboard-wrapper and dashboard-content
 * containers have the correct width and don't have any conflicting styles
 */

/* Target the dashboard-wrapper directly */
.dashboard-wrapper {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  min-height: 0 !important; /* Override min-height: 100vh */
  height: auto !important;
}

/* Target the dashboard-content directly */
.dashboard-content {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  position: relative !important;
  min-height: 0 !important; /* Override min-height */
  height: auto !important;
}

/* Ensure the sidebar has the correct width and position */
.modern-sidebar {
  width: 280px !important;
  position: fixed !important;
  z-index: 900 !important;
}

/* Ensure the main-content-wrapper has the correct width and margin */
#main-content.main-content-wrapper {
  width: calc(100% - 280px) !important;
  margin-left: 280px !important;
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  min-height: 0 !important; /* Override min-height */
  height: auto !important;
}

/* Ensure the super-admin-content-container has the correct width */
#main-content.main-content-wrapper .super-admin-content-container {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  align-self: stretch !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .modern-sidebar {
    transform: translateX(-100%) !important;
  }

  .modern-sidebar.open {
    transform: translateX(0) !important;
  }

  #main-content.main-content-wrapper {
    width: 100% !important;
    margin-left: 0 !important;
  }
}
