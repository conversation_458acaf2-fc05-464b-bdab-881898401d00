/* Fix Dashboard Wrapper Height
 * This file fixes the excessive height in the dashboard wrapper
 * by overriding min-height properties and ensuring content-based heights
 */

/* Fix for the dashboard-wrapper to prevent excessive height */
.dashboard-wrapper {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  margin-top: 52px !important; /* Match header height */
  padding-top: 0 !important;
  overflow-y: visible !important;
}

/* Fix for the dashboard-content to prevent excessive height */
.dashboard-content {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  overflow-y: visible !important;
}

/* Fix for the main-content-wrapper to prevent excessive height */
.main-content-wrapper {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  overflow-y: visible !important;
}

/* Fix for the modern-main-content to prevent excessive height */
.modern-main-content {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  padding: 1.5rem !important;
  overflow-y: visible !important;
}

/* Fix for the container-fluid to have appropriate padding */
.container-fluid {
  padding: 1.5rem !important;
}

/* Fix for the modern-loading-container to remove fixed height */
.modern-loading-container {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
  padding: 1.5rem 0 !important; /* Reduced padding */
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Fix for the modern-empty-state to have appropriate padding */
.modern-empty-state {
  padding: 1.5rem 0 !important;
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
}

/* Fix for the modern-card to have appropriate margin */
.modern-card {
  margin-bottom: 1rem !important;
}

/* Fix for the last modern-card to have no margin */
.modern-card:last-child {
  margin-bottom: 0 !important;
}

/* Fix for the content-table-card to have no margin */
.content-table-card {
  margin-bottom: 0 !important;
}

/* Fix for the modern-table-container to have appropriate height */
.modern-table-container {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
  max-height: none !important;
  overflow-y: visible !important;
}

/* Fix for the modern-card-body to have appropriate padding */
.modern-card-body {
  padding: 1rem !important;
  padding-bottom: 0.5rem !important;
}

/* Fix for media queries */
@media (max-width: 991px) {
  .dashboard-wrapper {
    min-height: 0 !important; /* Firefox compatible */
    height: auto !important;
  }
}

@media (max-width: 768px) {
  .dashboard-wrapper {
    min-height: 0 !important; /* Firefox compatible */
    height: auto !important;
  }
}
