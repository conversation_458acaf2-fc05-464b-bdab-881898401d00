/* Fix Super Admin Container
 * This file ensures that the super admin dashboard container uses the proper width and padding
 * to match the user dashboard layout
 */

/* Ensure the container has the proper width and padding */
.modern-main-content .container-fluid {
  width: 100% !important; /* Use full width */
  max-width: none !important; /* Remove max-width to use full width */
  margin: 0 !important; /* Remove margin to use full width */
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* Ensure the banner takes the correct width */
.modern-main-content .enhanced-dashboard-banner {
  width: 100% !important; /* Use 100% of the container width, not viewport width */
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 1.5rem !important;
  border-radius: 12px !important; /* Restore border radius for better appearance */
  align-self: stretch !important; /* Ensure it stretches to full width */
  position: relative !important; /* Ensure it's positioned relative to its container */
  overflow: hidden !important; /* Ensure content doesn't overflow */
}

/* Ensure the banner content is properly aligned */
.enhanced-dashboard-banner .banner-main {
  width: 100% !important; /* Use full width */
  max-width: none !important; /* Remove max-width to use full width */
  margin: 0 !important; /* Remove margin to use full width */
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* Ensure the breadcrumb has proper spacing */
.modern-main-content .modern-breadcrumb {
  width: 100% !important; /* Use full width */
  max-width: none !important; /* Remove max-width to use full width */
  margin: 0 !important; /* Remove margin to use full width */
  margin-bottom: 1rem !important;
  padding: 0 1.5rem !important; /* Add horizontal padding */
}

/* Ensure the notification has proper width */
.modern-main-content .modern-notification {
  width: 100% !important; /* Use full width */
  max-width: none !important; /* Remove max-width to use full width */
  margin: 0 !important; /* Remove margin to use full width */
  margin-bottom: 1rem !important;
  padding: 0 1.5rem !important; /* Add horizontal padding */
}

/* Ensure the stats grid uses the full width */
.modern-main-content .modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Ensure the content grid uses the full width */
.modern-main-content .modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important; /* 2/3 for main content, 1/3 for sidebar */
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Ensure the quick links section uses the full width */
.modern-main-content .modern-quick-links {
  width: 100% !important;
  margin-bottom: 2rem !important;
}

/* Ensure the quick links grid uses the full width */
.modern-main-content .modern-quick-links-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  width: 100% !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .modern-main-content .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .modern-main-content .modern-content-grid {
    grid-template-columns: 1fr !important; /* Stack on smaller screens */
  }

  .modern-main-content .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .modern-main-content .container-fluid {
    width: 100% !important;
    padding: 1rem !important;
  }

  .modern-main-content .modern-stats-grid,
  .modern-main-content .modern-quick-links-grid {
    grid-template-columns: 1fr !important;
  }
}
