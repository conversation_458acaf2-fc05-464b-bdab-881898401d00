/* Fix Super Admin Dashboard Display
 * This file provides a comprehensive fix for display issues in the Super Admin Dashboard
 */

/* Reset any problematic styles */
.dashboard-wrapper,
.dashboard-content,
.main-content-wrapper,
.super-admin-content-container,
.modern-main-content,
.container-fluid {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  overflow: visible !important;
}

/* Ensure proper layout structure */
.dashboard-wrapper {
  display: flex !important;
  flex-direction: column !important;
  min-height: 100vh !important;
  width: 100% !important;
}

.dashboard-content {
  display: flex !important;
  flex: 1 !important;
  position: relative !important;
  width: 100% !important;
}

.main-content-wrapper {
  flex: 1 !important;
  margin-left: 280px !important; /* Width of the sidebar */
  width: calc(100% - 280px) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  margin-top: 70px !important; /* Space for header */
}

.super-admin-content-container {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

.modern-main-content {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

.container-fluid {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* Fix z-index issues */
.header {
  z-index: 1050 !important;
}

.modern-sidebar {
  z-index: 1040 !important;
}

.main-content-wrapper {
  z-index: 1 !important;
}

.super-admin-content-container {
  z-index: 2 !important;
}

.modern-main-content {
  z-index: 3 !important;
}

.container-fluid {
  z-index: 4 !important;
}

/* Fix positioning issues */
.main-content-wrapper {
  position: relative !important;
}

.super-admin-content-container {
  position: relative !important;
}

.modern-main-content {
  position: relative !important;
}

.container-fluid {
  position: relative !important;
}

/* Fix height issues */
.main-content-wrapper {
  min-height: calc(100vh - 70px) !important;
  height: auto !important;
}

.super-admin-content-container {
  min-height: 200px !important;
  height: auto !important;
}

.modern-main-content {
  min-height: 200px !important;
  height: auto !important;
}

/* Fix width issues */
.modern-main-content.super-admin-dashboard {
  width: 90% !important;
  max-width: 1800px !important;
}

/* Fix margin issues */
.modern-main-content.super-admin-dashboard {
  margin: 0 auto !important;
}

/* Fix padding issues */
.modern-main-content.super-admin-dashboard {
  padding: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .main-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }
  
  .modern-main-content,
  .container-fluid {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .modern-main-content,
  .container-fluid {
    width: 100% !important;
  }
}
