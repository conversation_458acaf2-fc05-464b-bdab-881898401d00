/* Fix User Avatar Size
 * This file ensures the user avatar size in the user dashboard banner matches the super admin dashboard banner
 */

/* Fix the user avatar size in the user dashboard banner */
.enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar {
  width: 150px !important; /* Increased from 120px to match super admin dashboard */
  height: 150px !important; /* Increased from 120px to match super admin dashboard */
  border-radius: 50% !important;
  overflow: hidden !important;
  border: 4px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
  background-color: #f3f4f6 !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  margin: 0 !important; /* Reset any margin that might be affecting the avatar */
}

/* Fix the avatar image to fill the container */
.enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar .avatar-image,
.enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

/* Fix the avatar placeholder to fill the container */
.enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar .avatar-placeholder {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #359e04 !important;
  color: white !important;
  font-size: 4rem !important; /* Increased from 3rem to match the larger avatar */
  font-weight: 600 !important;
}

/* Fix the avatar wrapper to accommodate the larger avatar */
.enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar-wrapper {
  position: relative !important;
  margin-right: 2.5rem !important; /* Increased from 2rem to provide more space */
  margin-left: -30px !important; /* Keep the left offset */
}

/* Fix the avatar status indicator position */
.enhanced-dashboard-banner:not(.super-admin-banner) .avatar-status-indicator {
  position: absolute !important;
  bottom: 5px !important; /* Adjusted to match the larger avatar */
  right: 5px !important; /* Adjusted to match the larger avatar */
  z-index: 5 !important;
}

/* Fix the verification badge size */
.enhanced-dashboard-banner:not(.super-admin-banner) .verification-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 5px 12px !important; /* Increased from 4px 10px to match the larger avatar */
  border-radius: 20px !important;
  font-size: 0.875rem !important; /* Increased from 0.75rem to match the larger avatar */
  font-weight: 600 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Fix the user info spacing to accommodate the larger avatar */
.enhanced-dashboard-banner:not(.super-admin-banner) .user-info {
  margin-left: 0.5rem !important; /* Add a bit of space between avatar and info */
}

/* Fix the user name font size to match the larger avatar */
.enhanced-dashboard-banner:not(.super-admin-banner) .user-name {
  font-size: 2.25rem !important; /* Increased from 2rem to match the larger avatar */
  margin-bottom: 0.75rem !important; /* Increased from 0.5rem to match the larger avatar */
}

/* Fix the greeting font size to match the larger avatar */
.enhanced-dashboard-banner:not(.super-admin-banner) .greeting {
  font-size: 1.375rem !important; /* Increased from 1.25rem to match the larger avatar */
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar {
    width: 130px !important; /* Slightly smaller on medium screens */
    height: 130px !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar-wrapper {
    margin-right: 2rem !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .user-name {
    font-size: 2rem !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .greeting {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 768px) {
  .enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar {
    width: 120px !important; /* Smaller on mobile screens but still larger than default */
    height: 120px !important;
    margin: 0 auto !important; /* Center on mobile */
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar-wrapper {
    margin-right: 0 !important;
    margin-bottom: 1.5rem !important;
    margin-left: 0 !important; /* Reset left offset on mobile */
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .avatar-status-indicator {
    bottom: 3px !important;
    right: 3px !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .user-name {
    font-size: 1.75rem !important;
    text-align: center !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .greeting {
    font-size: 1.125rem !important;
    text-align: center !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .user-info {
    margin-left: 0 !important;
    text-align: center !important;
  }
}

@media (max-width: 576px) {
  .enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar {
    width: 100px !important; /* Even smaller on very small screens */
    height: 100px !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .user-avatar .avatar-placeholder {
    font-size: 3rem !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .verification-badge {
    padding: 4px 10px !important;
    font-size: 0.75rem !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .user-name {
    font-size: 1.5rem !important;
  }

  .enhanced-dashboard-banner:not(.super-admin-banner) .greeting {
    font-size: 1rem !important;
  }
}
