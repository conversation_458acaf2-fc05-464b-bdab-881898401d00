/* Fix Super Admin Dashboard Final
 * This file provides the final fixes for the super admin dashboard width issues
 * by ensuring all content elements use the full width of the available space
 */

/* ===== OVERRIDE ALL CONFLICTING WIDTH SETTINGS ===== */

/* Override any width settings in the super admin dashboard */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content,
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content > *,
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid,
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner {
  width: 100% !important;
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  box-sizing: border-box !important;
  align-self: stretch !important;
}

/* Ensure the container-fluid has proper padding */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid {
  padding: 1.5rem !important;
}

/* Ensure the enhanced-dashboard-banner has proper border radius */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner {
  border-radius: 16px !important;
  margin-bottom: 1.5rem !important;
}

/* ===== ENSURE PROPER GRID LAYOUTS ===== */

/* Ensure the modern-stats-grid uses the full width */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-stats-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Ensure the modern-content-grid uses the full width */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Ensure the main-content-area uses the full width */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid .main-content-area {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure the sidebar-content uses the full width */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid .sidebar-content {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1200px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }
  
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid {
    padding: 1rem !important;
  }
}
