/* Super Admin Direct Fix
 * This file provides a direct fix for the Super Admin Dashboard content visibility issues
 * by using !important to override any conflicting styles and ensuring content is visible
 */

/* ===== RESET ALL PROBLEMATIC STYLES ===== */

/* Reset display properties */
.dashboard-wrapper,
.dashboard-content,
.main-content-wrapper,
.super-admin-content-container,
.modern-main-content,
.modern-main-content.super-admin-dashboard,
.container-fluid,
.modern-stats-grid,
.modern-content-grid,
.modern-section-header,
.modern-card,
.modern-notification,
.modern-breadcrumb,
.dashboard-footer,
.enhanced-dashboard-banner,
.enhanced-dashboard-banner.super-admin-banner {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  overflow: visible !important;
  position: static !important;
  transform: none !important;
  pointer-events: auto !important;
  z-index: auto !important;
}

/* ===== LAYOUT STRUCTURE ===== */

/* Dashboard wrapper */
.dashboard-wrapper {
  display: flex !important;
  flex-direction: column !important;
  min-height: 100vh !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

/* Dashboard content */
.dashboard-content {
  display: flex !important;
  flex: 1 !important;
  position: relative !important;
  width: 100% !important;
  margin-top: 60px !important; /* Height of the header */
}

/* Main content wrapper */
.main-content-wrapper {
  flex: 1 !important;
  margin-left: 280px !important; /* Width of the sidebar */
  width: calc(100% - 280px) !important;
  margin-top: 70px !important; /* Space for header */
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  min-height: calc(100vh - 70px) !important;
  height: auto !important;
  padding: 0 !important;
}

/* Super admin content container */
.super-admin-content-container {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  min-height: 200px !important;
  height: auto !important;
}

/* Modern main content */
.modern-main-content,
.modern-main-content.super-admin-dashboard {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  min-height: 200px !important;
  height: auto !important;
  padding: 0 !important;
}

/* Container fluid */
.container-fluid {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* ===== DASHBOARD BANNER ===== */

/* Enhanced dashboard banner */
.enhanced-dashboard-banner,
.enhanced-dashboard-banner.super-admin-banner {
  width: 100% !important;
  max-width: 100% !important;
  margin-top: 10px !important;
  margin-bottom: 2rem !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  position: relative !important;
  z-index: 1 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* ===== GRID LAYOUTS ===== */

/* Modern stats grid */
.modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Modern content grid */
.modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* ===== SECTION HEADERS ===== */

/* Modern section header */
.modern-section-header {
  margin-bottom: 1.5rem !important;
  padding: 0 !important;
  background-color: transparent !important;
  width: 100% !important;
}

/* ===== CARDS ===== */

/* Modern card */
.modern-card {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  margin-bottom: 1.5rem !important;
  width: 100% !important;
}

/* ===== FOOTER ===== */

/* Dashboard footer */
.dashboard-footer {
  width: 100% !important;
  margin-top: auto !important;
  padding: 1.5rem !important;
  background-color: white !important;
  border-top: 1px solid #e5e7eb !important;
  text-align: center !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 991px) {
  .main-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }
  
  .modern-main-content,
  .modern-main-content.super-admin-dashboard,
  .container-fluid {
    width: 95% !important;
  }
  
  .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  .modern-main-content,
  .modern-main-content.super-admin-dashboard,
  .container-fluid {
    width: 100% !important;
  }
  
  .enhanced-dashboard-banner,
  .enhanced-dashboard-banner.super-admin-banner {
    border-radius: 0 !important;
  }
}
