/* Super Admin Content Page Styles
 * This file contains modern styling for the super admin content page
 * to match the layout, design, and styling of the user dashboard
 */

/* Modern Loading Container */
.modern-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0; /* Reduced padding to minimize empty space */
  height: auto;
  min-height: 0;
}

.modern-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(53, 158, 4, 0.1);
  border-radius: 50%;
  border-top-color: #359e04;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Modern Section Header */
.modern-section-header {
  margin-bottom: 2rem;
}

.modern-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--dark);
}

.modern-section-subtitle {
  font-size: 0.9rem;
  color: var(--gray-600);
  margin-bottom: 0;
}

/* Modern Card */
.modern-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 1.5rem;
  border: none;
}

.modern-card-header {
  padding: 1rem 1.25rem;
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0;
  color: var(--dark);
}

.modern-card-body {
  padding: 1.25rem;
}

/* Modern Table */
.modern-table-container {
  overflow-x: auto;
}

.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table-header {
  background-color: var(--gray-100);
}

.modern-table-cell {
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--gray-200);
}

.modern-table-row:hover {
  background-color: var(--gray-50);
}

/* Modern Badge */
.modern-badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  text-transform: capitalize;
}

.modern-badge-primary {
  background-color: var(--primary-light);
  color: var(--primary);
}

.modern-badge-success {
  background-color: var(--success-light);
  color: var(--success);
}

.modern-badge-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.modern-badge-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.modern-badge-info {
  background-color: var(--info-light);
  color: var(--info);
}

.modern-badge-secondary {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

/* Modern Button */
.modern-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modern-button-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
}

.modern-button-primary {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.modern-button-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.modern-button-success {
  color: white;
  background-color: var(--success);
  border-color: var(--success);
}

.modern-button-success:hover {
  background-color: var(--success-dark);
  border-color: var(--success-dark);
}

.modern-button-danger {
  color: white;
  background-color: var(--danger);
  border-color: var(--danger);
}

.modern-button-danger:hover {
  background-color: var(--danger-dark);
  border-color: var(--danger-dark);
}

.modern-button-outline-primary {
  color: var(--primary);
  background-color: transparent;
  border-color: var(--primary);
}

.modern-button-outline-primary:hover {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.modern-button-outline-success {
  color: var(--success);
  background-color: transparent;
  border-color: var(--success);
}

.modern-button-outline-success:hover {
  color: white;
  background-color: var(--success);
  border-color: var(--success);
}

.modern-button-outline-danger {
  color: var(--danger);
  background-color: transparent;
  border-color: var(--danger);
}

.modern-button-outline-danger:hover {
  color: white;
  background-color: var(--danger);
  border-color: var(--danger);
}

/* Modern Filter Pills */
.modern-filter-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.modern-filter-pill {
  display: inline-block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 2rem;
  cursor: pointer;
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: none;
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
}

.modern-filter-pill:hover {
  background-color: var(--gray-200);
}

.modern-filter-pill.active {
  background-color: var(--primary);
  color: white;
}

/* Modern Actions */
.modern-actions {
  display: flex;
  gap: 0.5rem;
}

.modern-actions-cell {
  white-space: nowrap;
}

/* Modern Empty State */
.modern-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.modern-empty-state-icon {
  font-size: 3rem;
  color: var(--gray-400);
  margin-bottom: 1rem;
}

.modern-empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.modern-empty-state-description {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 1.5rem;
  max-width: 400px;
}

/* Modern Toggle Switch */
.modern-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.modern-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.modern-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: .4s;
  border-radius: 34px;
}

.modern-switch-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.modern-switch input:checked + .modern-switch-slider {
  background-color: var(--success);
}

.modern-switch input:focus + .modern-switch-slider {
  box-shadow: 0 0 1px var(--success);
}

.modern-switch input:checked + .modern-switch-slider:before {
  transform: translateX(20px);
}

.modern-switch-label {
  margin-left: 50px;
  font-size: 0.875rem;
  color: var(--gray-700);
}

/* Modern Modal */
.modern-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.modern-modal {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-modal-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0;
  color: var(--dark);
}

.modern-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  color: var(--gray-500);
  cursor: pointer;
}

.modern-modal-body {
  padding: 1.25rem;
}

.modern-modal-footer {
  padding: 1.25rem;
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Fix for content section to prevent excessive empty space */
.content-table-card {
  margin-bottom: 0 !important;
}

/* Ensure the table container doesn't have fixed height */
.modern-table-container {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
  max-height: none !important;
}

/* Ensure the card body doesn't have excessive padding */
.modern-card-body {
  padding-bottom: 0.5rem !important;
}

/* Ensure the pagination has minimal margin */
.modern-pagination {
  margin-top: 0.5rem !important;
  margin-bottom: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modern-filter-pills {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
  }
  
  .modern-filter-pill {
    flex-shrink: 0;
  }
  
  .modern-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .modern-button {
    width: 100%;
  }
}
