/* Fix Enhanced Dashboard Banner
 * This file overrides the inline styles in the EnhancedDashboardBanner component
 * to ensure it uses the full width in the super admin dashboard
 */

/* Target the enhanced-dashboard-banner directly when inside super-admin-dashboard */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner {
  position: relative !important;
  width: 100% !important;
  min-height: 320px !important;
  overflow: hidden !important;
  margin-bottom: 2rem !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-top: 0 !important;
  border-radius: 16px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
  transition: box-shadow 0.5s ease, background-color 0.5s ease !important;
  max-width: 100% !important;
  align-self: stretch !important;
}

/* Target the banner-background */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-background {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1 !important;
}

/* Target the banner-overlay */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, rgba(53, 158, 4, 0.9), rgba(53, 158, 4, 0.7)) !important;
  z-index: 2 !important;
}

/* Target the banner-content */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-content {
  position: relative !important;
  z-index: 4 !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  padding: 2rem !important;
  color: white !important;
}

/* Target the banner-main */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-main {
  display: flex !important;
  justify-content: space-between !important;
  flex: 1 !important;
}

/* Target the banner-stats */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-stats {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
}

/* Target the stats-grid */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .stats-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 2rem !important;
  margin-top: 1rem !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-main {
    flex-direction: column !important;
    gap: 2rem !important;
  }

  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-stats {
    width: 100% !important;
    align-items: flex-start !important;
  }
}

@media (max-width: 768px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner {
    min-height: auto !important;
  }

  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .banner-content {
    padding: 1.5rem !important;
  }

  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .stats-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1rem !important;
  }
}

@media (max-width: 576px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner .stats-grid {
    grid-template-columns: 1fr !important;
  }
}
