/* Super Admin Dashboard Consistency
 * This file provides a comprehensive solution to ensure the Super Admin Dashboard
 * matches the design, layout, and styling of the User Dashboard
 */

/* ===== MAIN CONTAINER FIXES ===== */

/* Ensure the dashboard wrapper has proper styling */
.dashboard-wrapper {
  display: flex !important;
  flex-direction: column !important;
  min-height: 100vh !important;
  width: 100% !important;
  overflow-x: hidden !important;
  background-color: var(--gray-50) !important;
}

/* Ensure the dashboard content has proper styling */
.dashboard-content {
  display: flex !important;
  flex: 1 !important;
  position: relative !important;
  width: 100% !important;
  margin-top: 60px !important; /* Height of the header */
}

/* Ensure the main content wrapper has proper styling */
.main-content-wrapper {
  flex: 1 !important;
  padding: 0 !important;
  margin-left: 280px !important; /* Width of the sidebar */
  width: calc(100% - 280px) !important;
  min-height: calc(100vh - 60px) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  overflow-x: hidden !important;
}

/* ===== CONTENT CONTAINER FIXES ===== */

/* Ensure the super admin content container has proper styling */
.super-admin-content-container {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Ensure the modern main content has proper styling */
.modern-main-content {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Ensure the container fluid has proper styling */
.container-fluid {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* ===== BANNER FIXES ===== */

/* Ensure the enhanced dashboard banner has proper styling */
.enhanced-dashboard-banner {
  width: 100% !important;
  max-width: 100% !important;
  margin-bottom: 2rem !important;
  margin-top: 10px !important; /* 10px top margin for the dashboard banner */
  border-radius: 16px !important; /* Rounded corners */
  overflow: hidden !important;
  position: relative !important;
}

/* Ensure the super admin banner has proper styling */
.enhanced-dashboard-banner.super-admin-banner {
  border-radius: 16px !important; /* Rounded corners */
}

/* ===== GRID LAYOUT FIXES ===== */

/* Ensure the modern stats grid has proper styling */
.modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Ensure the modern content grid has proper styling */
.modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* ===== SECTION HEADER FIXES ===== */

/* Ensure all section headers have consistent styling */
.modern-section-header {
  margin-bottom: 1.5rem !important;
  padding: 0 !important;
  background-color: transparent !important; /* No white backgrounds */
  width: 100% !important;
}

.modern-section-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: var(--gray-900) !important;
  margin: 0 !important;
}

.modern-section-subtitle {
  font-size: 0.875rem !important;
  color: var(--gray-600) !important;
  margin-top: 0.25rem !important;
}

/* ===== CARD FIXES ===== */

/* Ensure all cards have proper styling */
.modern-card {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  margin-bottom: 1.5rem !important;
  width: 100% !important;
}

.modern-card-header {
  padding: 1rem 1.5rem !important;
  border-bottom: 1px solid var(--gray-200) !important;
  background-color: transparent !important; /* No white backgrounds */
}

.modern-card-body {
  padding: 1.5rem !important;
  height: auto !important; /* Flexible height based on content */
  min-height: 0 !important;
}

/* ===== TABLE FIXES ===== */

/* Ensure all tables have proper styling */
.modern-table-container {
  width: 100% !important;
  overflow-x: auto !important;
  height: auto !important; /* Flexible height based on content */
  min-height: 0 !important;
}

.modern-table {
  width: 100% !important;
  border-collapse: collapse !important;
}

.modern-table-header {
  background-color: var(--gray-50) !important;
  color: var(--gray-700) !important;
  font-weight: 600 !important;
  text-align: left !important;
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid var(--gray-200) !important;
}

.modern-table-cell {
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid var(--gray-200) !important;
  color: var(--gray-700) !important;
}

/* ===== BREADCRUMB FIXES ===== */

/* Ensure breadcrumb navigation matches width and alignment */
.modern-breadcrumb {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 1rem auto !important;
  padding: 0 !important;
}

/* ===== FOOTER FIXES ===== */

/* Ensure the footer extends across both sidebar and main content */
.dashboard-footer {
  width: 100% !important;
  margin-top: auto !important;
  padding: 1.5rem !important;
  background-color: white !important;
  border-top: 1px solid var(--gray-200) !important;
  text-align: center !important;
}

/* ===== COLOR FIXES ===== */

/* Ensure primary color is used consistently */
.primary-color,
.primary-bg,
.btn-primary,
.badge-primary,
.alert-primary,
.bg-primary {
  color: white !important;
  background-color: #359e04 !important; /* Primary green color */
  border-color: #359e04 !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1200px) {
  .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 991px) {
  .main-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .modern-main-content,
  .container-fluid,
  .modern-breadcrumb {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .modern-main-content,
  .container-fluid,
  .modern-breadcrumb {
    width: 100% !important;
  }

  .enhanced-dashboard-banner {
    border-radius: 0 !important;
  }
}
