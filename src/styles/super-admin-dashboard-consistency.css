/* Super Admin Dashboard Consistency
 * This file provides a comprehensive solution to ensure the Super Admin Dashboard
 * matches the design, layout, and styling of the User Dashboard with all requirements
 */

/* ===== CSS VARIABLES ===== */
:root {
  --primary-color: #359e04;
  --primary-color-hover: #2d8003;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

/* ===== MAIN CONTAINER FIXES ===== */

/* Ensure the dashboard wrapper has proper styling */
.dashboard-wrapper {
  display: flex !important;
  flex-direction: column !important;
  min-height: 100vh !important;
  width: 100% !important;
  overflow-x: hidden !important;
  background-color: var(--gray-50) !important;
}

/* Ensure the dashboard content has proper styling with 70px top margin */
.dashboard-content {
  display: flex !important;
  flex: 1 !important;
  position: relative !important;
  width: 100% !important;
  margin-top: 70px !important; /* 70px top margin for main content to prevent header overlapping */
}

/* Ensure the main content wrapper has proper styling */
.main-content-wrapper {
  flex: 1 !important;
  padding: 0 !important;
  margin-left: 280px !important; /* Width of the sidebar */
  width: calc(100% - 280px) !important;
  min-height: calc(100vh - 70px) !important; /* Adjusted for 70px header margin */
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  overflow-x: hidden !important;
}

/* ===== CONTENT CONTAINER FIXES ===== */

/* Ensure the super admin content container has proper styling */
.super-admin-content-container {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Ensure the modern main content has 90% width and proper styling */
.modern-main-content {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Ensure the container fluid has 90% width and proper styling */
.container-fluid {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* ===== BANNER FIXES ===== */

/* Ensure the enhanced dashboard banner has proper styling with 10px top margin and rounded corners */
.enhanced-dashboard-banner {
  width: 90% !important; /* Match other dashboard elements */
  max-width: 1800px !important;
  margin: 10px auto 2rem auto !important; /* 10px top margin for the dashboard banner, centered */
  border-radius: 16px !important; /* Rounded corners */
  overflow: hidden !important;
  position: relative !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure the super admin banner has proper styling and doesn't overlap with sidebar */
.enhanced-dashboard-banner.super-admin-banner {
  border-radius: 16px !important; /* Rounded corners */
  width: 90% !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* ===== GRID LAYOUT FIXES ===== */

/* Ensure the modern stats grid has proper styling */
.modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Ensure the modern content grid has proper styling */
.modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* ===== SECTION HEADER FIXES ===== */

/* Ensure all section headers have consistent styling without white backgrounds */
.modern-section-header {
  margin-bottom: 1.5rem !important;
  padding: 0 !important;
  background-color: transparent !important; /* No white backgrounds */
  width: 100% !important;
}

.modern-section-title {
  font-size: 1.75rem !important;
  font-weight: 600 !important;
  color: var(--gray-900) !important;
  margin: 0 !important;
}

.modern-section-subtitle {
  font-size: 0.875rem !important;
  color: var(--gray-600) !important;
  margin-top: 0.25rem !important;
}

/* ===== CARD FIXES ===== */

/* Ensure all cards have proper styling with flexible height */
.modern-card {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  margin-bottom: 1.5rem !important;
  width: 100% !important;
  height: auto !important; /* Flexible height based on content */
  min-height: 0 !important;
}

.modern-card-header {
  padding: 1rem 1.5rem !important;
  border-bottom: 1px solid var(--gray-200) !important;
  background-color: transparent !important; /* No white backgrounds */
}

.modern-card-body {
  padding: 1.5rem !important;
  height: auto !important; /* Flexible height based on content */
  min-height: 0 !important;
}

/* Specific fixes for search card body to reduce spacing */
.search-card-body {
  padding: 1rem 1.5rem !important;
  margin-bottom: 0 !important;
}

/* ===== TABLE FIXES ===== */

/* Ensure all tables have proper styling with flexible height */
.modern-table-container {
  width: 100% !important;
  overflow-x: auto !important;
  height: auto !important; /* Flexible height based on content */
  min-height: 0 !important;
}

.modern-table {
  width: 100% !important;
  border-collapse: collapse !important;
}

.modern-table-header {
  background-color: var(--gray-50) !important;
  color: var(--gray-700) !important;
  font-weight: 600 !important;
  text-align: left !important;
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid var(--gray-200) !important;
}

.modern-table-cell {
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid var(--gray-200) !important;
  color: var(--gray-700) !important;
}

/* ===== BREADCRUMB FIXES ===== */

/* Ensure breadcrumb navigation matches width (90%) and alignment */
.modern-breadcrumb,
.breadcrumb-nav,
.breadcrumb-container {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 1rem auto !important;
  padding: 0 !important;
}

/* ===== FOOTER FIXES ===== */

/* Ensure the footer extends across both sidebar and main content areas */
.dashboard-footer {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100% !important;
  margin-top: auto !important;
  padding: 1.5rem !important;
  background-color: white !important;
  border-top: 1px solid var(--gray-200) !important;
  text-align: center !important;
  z-index: 1000 !important;
}

/* ===== COLOR FIXES ===== */

/* Ensure primary color #359e04 is used consistently throughout all dashboard sections */
.primary-color,
.primary-bg,
.btn-primary,
.badge-primary,
.alert-primary,
.bg-primary,
.modern-button-primary,
.modern-badge-primary {
  color: white !important;
  background-color: var(--primary-color) !important; /* Primary green color #359e04 */
  border-color: var(--primary-color) !important;
}

.primary-color:hover,
.btn-primary:hover,
.modern-button-primary:hover {
  background-color: var(--primary-color-hover) !important;
  border-color: var(--primary-color-hover) !important;
}

/* Ensure success color uses primary green */
.modern-button-success,
.modern-badge-success {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* ===== NOTIFICATION AND SUCCESS CARD FIXES ===== */

/* Ensure notification cards match dashboard banner styling */
.notification-card,
.success-notification-card {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 1rem auto !important;
  border-radius: 16px !important;
}

/* ===== STATS SUMMARY FIXES ===== */

/* Ensure stats summary has proper styling */
.modern-stats-summary {
  display: flex !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

.modern-stat-item {
  flex: 1 !important;
  background: white !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1200px) {
  .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .modern-content-grid {
    grid-template-columns: 1fr !important;
  }

  .modern-stats-summary {
    flex-direction: column !important;
  }
}

@media (max-width: 991px) {
  .main-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .modern-main-content,
  .container-fluid,
  .modern-breadcrumb,
  .enhanced-dashboard-banner,
  .notification-card,
  .success-notification-card {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .modern-main-content,
  .container-fluid,
  .modern-breadcrumb,
  .enhanced-dashboard-banner,
  .notification-card,
  .success-notification-card {
    width: 100% !important;
  }

  .enhanced-dashboard-banner {
    border-radius: 8px !important;
    margin: 5px auto 1rem auto !important;
  }

  .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }
}

/* ===== SPECIFIC SECTION FIXES ===== */

/* Ensure all Super Admin sections (Users, Businesses, Verifications, Listings) follow the same layout */
.users-table-card,
.businesses-table-card,
.verifications-table-card,
.listings-table-card {
  height: auto !important; /* Flexible height based on content */
  min-height: 0 !important;
}

/* Remove excessive spacing in specific sections */
.modern-search-filter-container {
  margin-bottom: 0 !important;
}

.modern-filter-container {
  margin-bottom: 0 !important;
}

/* Ensure proper spacing for action buttons */
.modern-action-buttons {
  display: flex !important;
  gap: 0.75rem !important;
  align-items: center !important;
}

/* ===== FINAL LAYOUT CONSISTENCY ===== */

/* Ensure all dashboard sections use the same layout pattern */
.super-admin-dashboard .modern-main-content,
.super-admin-dashboard-page .modern-main-content {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
}

/* Ensure content areas fully utilize available space without empty areas on the right side */
.super-admin-content-container .modern-main-content {
  align-items: stretch !important;
  width: 90% !important;
}

/* Fix for any remaining width utilization issues */
.main-content-wrapper .super-admin-content-container {
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
}
