/* Fix Notification Card Width
 * This file ensures that the notification card matches the width and styling of the dashboard banner
 */

/* Target the notification card specifically */
.modern-main-content .modern-notification {
  width: 90% !important;
  max-width: 1800px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* Ensure the notification maintains its existing styling */
.modern-main-content .modern-notification.success {
  border-left-color: var(--success) !important;
}

.modern-main-content .modern-notification.success::before {
  background: linear-gradient(45deg, rgba(16, 185, 129, 0.05), transparent) !important;
}

.modern-main-content .modern-notification .notification-icon {
  background-color: var(--success-light) !important;
  color: var(--success) !important;
}

/* Responsive styles */
@media (max-width: 991px) {
  .modern-main-content .modern-notification {
    width: 95% !important;
    border-radius: 12px !important;
  }
}

@media (max-width: 768px) {
  .modern-main-content .modern-notification {
    width: 95% !important;
    border-radius: 12px !important;
  }
}
