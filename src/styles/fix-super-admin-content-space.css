/* Fix Super Admin Content Space
 * This file fixes the excessive empty space in the super admin dashboard sections
 * by ensuring content areas have flexible height based on actual content
 */

/* Fix for the modern-loading-container to remove fixed height */
.modern-loading-container {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
  padding: 1.5rem 0 !important; /* Reduced padding */
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Ensure the content table card has no excessive space */
.content-table-card,
.users-table-card,
.admins-table-card,
.listings-table-card,
.businesses-table-card {
  margin-bottom: 0 !important;
}

/* Ensure the table container doesn't have fixed height */
.modern-table-container {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
  max-height: none !important;
  overflow-y: visible !important;
}

/* Ensure the card body doesn't have excessive padding */
.modern-card-body {
  padding: 1rem !important;
  padding-bottom: 0.5rem !important;
}

/* Ensure the pagination has minimal margin */
.modern-pagination {
  margin-top: 0.5rem !important;
  margin-bottom: 0 !important;
}

/* Fix for the dashboard-content to prevent excessive height */
.dashboard-content {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
}

/* Fix for the dashboard-wrapper to prevent excessive height */
.dashboard-wrapper {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
}

/* Fix for the modern-main-content to prevent excessive height */
.modern-main-content {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
}

/* Ensure the table doesn't have excessive height */
.modern-table {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
}

/* Ensure the table body doesn't have excessive height */
.modern-table-body {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
}

/* Ensure the section header has appropriate spacing */
.modern-section-header {
  margin-bottom: 1.5rem !important;
}

/* Ensure the filter pills section doesn't have excessive margin */
.modern-filter-pills {
  margin-bottom: 0.5rem !important;
}

/* Ensure the empty state doesn't have excessive padding */
.modern-empty-state {
  padding: 2rem 1rem !important;
}

/* Ensure the card header has appropriate padding */
.modern-card-header {
  padding: 0.75rem 1.25rem !important;
}

/* Ensure the section content has appropriate spacing */
.container-fluid {
  padding: 1.5rem !important;
}

/* Fix for the main-content-wrapper to prevent excessive height */
.main-content-wrapper {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  overflow-y: visible !important;
}

/* Fix for any row elements that might have margin-bottom */
.row {
  margin-bottom: 1rem !important;
}

/* Fix for the last row to have no bottom margin */
.row:last-child {
  margin-bottom: 0 !important;
}

/* Fix for the modern-stats-grid to have appropriate spacing */
.modern-stats-grid {
  margin-bottom: 1.5rem !important;
}

/* Fix for the modern-content-grid to have appropriate spacing */
.modern-content-grid {
  margin-bottom: 0 !important;
}
