/* Fix Dashboard Height
 * This file fixes the excessive height in dashboard layouts
 * by ensuring content areas have flexible height based on actual content
 */

/* Fix for the dashboard wrapper */
.dashboard-wrapper,
.modern-dashboard-wrapper {
  min-height: 0 !important;
  height: auto !important;
}

/* Fix for the dashboard content */
.dashboard-content,
.modern-dashboard-content {
  min-height: 0 !important;
  height: auto !important;
}

/* Fix for the main content wrapper */
.main-content-wrapper,
.modern-main-content-wrapper {
  min-height: 0 !important;
  height: auto !important;
}

/* Fix for the main content */
.main-content,
.modern-main-content {
  min-height: 0 !important;
  height: auto !important;
}

/* Fix for the container fluid */
.container-fluid {
  min-height: 0 !important;
  height: auto !important;
  padding-bottom: 1rem !important;
}

/* Fix for the dashboard content area */
.dashboard-content-area,
.modern-dashboard-content-area {
  min-height: 0 !important;
  height: auto !important;
}

/* Ensure the footer doesn't push content up */
.footer {
  position: relative !important;
  margin-top: 1rem !important;
}
