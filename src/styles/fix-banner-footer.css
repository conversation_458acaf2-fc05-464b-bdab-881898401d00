/* Fix Banner Footer
 * This file ensures the banner footer and quick action buttons are visible
 */

/* Ensure the banner footer is visible */
.enhanced-dashboard-banner .banner-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-top: 2rem !important;
  padding-top: 1.5rem !important;
  border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10 !important;
  position: relative !important;
}

/* Ensure the quick actions are visible */
.enhanced-dashboard-banner .quick-actions {
  display: flex !important;
  gap: 1rem !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 11 !important;
  position: relative !important;
  flex-wrap: wrap !important;
}

/* Ensure the quick action buttons are visible */
.enhanced-dashboard-banner .quick-action-button {
  display: flex !important;
  align-items: center !important;
  padding: 0.5rem 1rem !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  text-decoration: none !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  backdrop-filter: blur(5px) !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 12 !important;
  position: relative !important;
  margin-bottom: 0.5rem !important;
}

/* Ensure the quick action buttons hover effect works */
.enhanced-dashboard-banner .quick-action-button:hover {
  background-color: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px) !important;
}

/* Ensure the profile completion section is visible */
.enhanced-dashboard-banner .profile-completion {
  flex: 1 !important;
  margin-right: 2rem !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 11 !important;
  position: relative !important;
  min-width: 200px !important;
}

/* Fix for any potential overflow issues */
.enhanced-dashboard-banner .banner-content {
  overflow: visible !important;
  height: auto !important;
  min-height: 320px !important;
  padding-bottom: 2rem !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
}

/* Ensure the banner main doesn't hide the footer */
.enhanced-dashboard-banner .banner-main {
  flex: 0 1 auto !important; /* Allow the banner-main to shrink */
  margin-bottom: 0 !important;
  position: relative !important;
}

/* Fix for the button icons */
.enhanced-dashboard-banner .button-icon {
  margin-right: 0.5rem !important;
  display: inline-block !important;
}

/* Fix for the banner height to accommodate the footer */
.enhanced-dashboard-banner {
  min-height: 380px !important; /* Increased to accommodate footer */
  height: auto !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-dashboard-banner .banner-footer {
    flex-direction: column !important;
    gap: 1.5rem !important;
  }

  .enhanced-dashboard-banner .profile-completion {
    width: 100% !important;
    margin-right: 0 !important;
  }

  .enhanced-dashboard-banner .quick-actions {
    width: 100% !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
  }
}
