/* Modern Content Grid Styles
 * This file ensures that the content grid layout is consistent across the application
 */

/* Main content grid layout */
.modern-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr; /* 2/3 for main content, 1/3 for sidebar */
  gap: 1.5rem;
  margin-bottom: 2rem;
  width: 100%;
}

/* Main content area */
.main-content-area {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Sidebar content */
.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Activity grid */
.activity-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

/* Quick links grid */
.modern-quick-links-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  width: 100%;
}

/* Quick link card */
.modern-quick-link-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.modern-quick-link-card:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.quick-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 1rem;
  font-size: 1.25rem;
}

.quick-link-content {
  flex: 1;
}

.quick-link-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--gray-800);
}

.quick-link-description {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .modern-content-grid {
    grid-template-columns: 1fr; /* Stack on smaller screens */
  }
  
  .activity-grid {
    grid-template-columns: 1fr; /* Stack on smaller screens */
  }
  
  .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .modern-quick-links-grid {
    grid-template-columns: 1fr;
  }
}
