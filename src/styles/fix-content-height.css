/* Fix Content Height
 * This file ensures that content areas have flexible height based on actual content
 * rather than fixed or minimum height constraints that create excessive empty space
 */

/* Remove fixed height from all content containers */
.modern-main-content,
.dashboard-content-wrap,
.container-fluid,
.dashboard-section,
.modern-card,
.modern-card-body,
.modern-table-container,
.modern-section-content,
.section-content,
.dashboard-section-body,
.modern-loading-container {
  height: auto !important;
  min-height: 0 !important;
  max-height: none !important;
}

/* Ensure tables have proper height */
.modern-table-container {
  overflow-y: visible !important;
  overflow-x: auto !important;
}

/* Ensure cards have proper height */
.modern-card {
  margin-bottom: 1.5rem !important;
}

.modern-card-body {
  padding: 1.5rem !important;
}

/* Ensure loading containers have proper height */
.modern-loading-container {
  padding: 2rem 0 !important;
}

/* Ensure section headers have proper spacing */
.modern-section-header {
  margin-bottom: 1.5rem !important;
}

/* Ensure search card has proper spacing */
.search-card-body {
  padding: 1rem 1.5rem 0.75rem 1.5rem !important;
}

/* Ensure table card has proper spacing */
.table-card .modern-card-body {
  padding: 1rem 1.5rem 0.25rem 1.5rem !important;
}

/* Ensure users table card has proper spacing */
.users-table-card .modern-card-body {
  padding: 1rem 1.5rem 0.25rem 1.5rem !important;
}

/* Ensure businesses table card has proper spacing */
.businesses-table-card .modern-card-body {
  padding: 1rem 1.5rem 0.25rem 1.5rem !important;
}

/* Ensure verifications table card has proper spacing */
.verifications-table-card .modern-card-body {
  padding: 1rem 1.5rem 0.25rem 1.5rem !important;
}

/* Ensure listings table card has proper spacing */
.listings-table-card .modern-card-body {
  padding: 1rem 1.5rem 0.25rem 1.5rem !important;
}

/* Ensure pagination has proper spacing */
.modern-pagination {
  margin-top: 1rem !important;
  margin-bottom: 0 !important;
}

/* Ensure table has proper spacing */
.modern-table {
  margin-bottom: 0 !important;
}

/* Ensure table header has proper spacing */
.modern-table-header {
  padding: 0.75rem 1rem !important;
}

/* Ensure table cell has proper spacing */
.modern-table-cell {
  padding: 0.75rem 1rem !important;
}

/* Ensure actions cell has proper spacing */
.modern-actions-cell {
  display: flex !important;
  gap: 0.5rem !important;
  flex-wrap: wrap !important;
}

/* Ensure button has proper spacing */
.modern-button {
  padding: 0.5rem 1rem !important;
}

.modern-button-sm {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.875rem !important;
}

/* Ensure badge has proper spacing */
.modern-badge {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
  border-radius: 0.25rem !important;
}

/* Ensure search container has proper spacing */
.modern-search-container {
  margin-bottom: 1rem !important;
}

/* Ensure filter container has proper spacing */
.modern-filter-container {
  margin-bottom: 1rem !important;
}

/* Ensure action buttons have proper spacing */
.modern-action-buttons {
  margin-bottom: 1rem !important;
}

/* Ensure stats summary has proper spacing */
.modern-stats-summary {
  margin-bottom: 1.5rem !important;
}

/* Ensure stat item has proper spacing */
.modern-stat-item {
  padding: 1rem !important;
  margin-bottom: 0 !important;
}

/* Ensure empty state has proper spacing */
.modern-empty-state {
  padding: 2rem !important;
}

/* Ensure breadcrumb has proper spacing */
.modern-breadcrumb {
  margin-bottom: 1.5rem !important;
}
