/* Fix Super Admin Full Width
 * This file ensures that the super admin dashboard uses the full width available
 * by overriding any conflicting styles
 */

/* Set proper max-width and width for all elements */
.modern-main-content {
  max-width: 1800px !important;
  width: 95% !important;
}

.modern-main-content .modern-stats-grid,
.modern-main-content .modern-content-grid,
.modern-main-content .modern-quick-links,
.modern-main-content .modern-quick-links-grid,
.modern-main-content .main-content-area,
.modern-main-content .sidebar-content,
.modern-main-content .activity-grid {
  width: 100% !important;
}

/* Ensure the banner takes the correct width */
.enhanced-dashboard-banner {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto !important;
  margin-bottom: 1.5rem !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

/* Ensure the main content wrapper takes full width */
.main-content-wrapper {
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  width: calc(100% - 280px) !important;
  margin-left: 280px !important;
}

/* Ensure the super admin content container takes full width */
.super-admin-content-container {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  align-self: stretch !important;
}

/* Ensure the modern-main-content has proper width and margins */
.modern-main-content {
  width: 95% !important; /* Slightly less than 100% to match user dashboard */
  max-width: 1800px !important; /* Restore max-width for better appearance */
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  padding: 0 !important;
  margin: 0 auto !important; /* Center the content */
}

/* Ensure the container-fluid has proper width and margins */
.container-fluid {
  width: 95% !important; /* Slightly less than 100% to match user dashboard */
  padding: 1.5rem !important;
  margin: 0 auto !important; /* Center the container */
  box-sizing: border-box !important;
}

/* Ensure the modern-content-grid takes full width */
.modern-content-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  padding: 0 !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

/* Ensure the main-content-area takes full width */
.main-content-area {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
}

/* Ensure the sidebar-content takes full width */
.sidebar-content {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
}

/* Ensure the modern-quick-links takes full width */
.modern-quick-links {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

/* Ensure the modern-quick-links-grid takes full width */
.modern-quick-links-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  padding: 0 !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

/* Mobile styles */
@media (max-width: 991px) {
  .main-content-wrapper {
    width: 100% !important;
    margin-left: 0 !important;
  }

  .modern-content-grid {
    grid-template-columns: 1fr !important;
  }

  .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .modern-quick-links-grid {
    grid-template-columns: 1fr !important;
  }
}
