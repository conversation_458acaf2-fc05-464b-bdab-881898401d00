/* Override Dashboard Min Height
 * This file directly overrides the min-height: 100vh property in dashboard.css
 * with the highest specificity possible
 */

/* Override with high specificity */
html body .dashboard-wrapper,
body .dashboard-wrapper,
.dashboard-wrapper {
  min-height: 0 !important;
  height: auto !important;
}

/* Override with high specificity */
html body .dashboard-content,
body .dashboard-content,
.dashboard-content {
  min-height: 0 !important;
  height: auto !important;
}

/* Override with high specificity */
html body .main-content-wrapper,
body .main-content-wrapper,
.main-content-wrapper {
  min-height: 0 !important;
  height: auto !important;
}

/* Override with high specificity */
html body .modern-main-content,
body .modern-main-content,
.modern-main-content {
  min-height: 0 !important;
  height: auto !important;
}

/* Override with high specificity */
html body .container-fluid,
body .container-fluid,
.container-fluid {
  min-height: 0 !important;
  height: auto !important;
}

/* Override with high specificity */
html body .content-table-card,
body .content-table-card,
.content-table-card {
  margin-bottom: 0 !important;
}

/* Override with high specificity */
html body .modern-card-body,
body .modern-card-body,
.modern-card-body {
  padding-bottom: 0.5rem !important;
}

/* Override with high specificity */
html body .modern-table-container,
body .modern-table-container,
.modern-table-container {
  height: auto !important;
  min-height: 0 !important;
  max-height: none !important;
  overflow-y: visible !important;
}
