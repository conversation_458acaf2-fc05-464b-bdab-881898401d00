/* Enhanced Dashboard Banner Styles
 * This file contains all the styles for the EnhancedDashboardBanner component
 */

.enhanced-dashboard-banner {
  position: relative;
  width: 100%;
  min-height: 320px;
  overflow: hidden;
  margin-bottom: 2rem;
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  opacity: 1;
  transform: translateY(0);
  transition: box-shadow 0.5s ease, background-color 0.5s ease;
  max-width: 100%;
  align-self: stretch;
}

.enhanced-dashboard-banner.super-admin-banner {
  border-radius: 16px;
  margin-top: 0;
}

.enhanced-dashboard-banner.animated {
  animation: gentle-pulse 0.5s ease-in-out;
}

@keyframes gentle-pulse {
  0% { opacity: 0.95; }
  100% { opacity: 1; }
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(53, 158, 4, 0.9), rgba(53, 158, 4, 0.7));
  z-index: 2;
}

.banner-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  overflow: hidden;
}

.particle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  pointer-events: none;
  opacity: 0.5;
}

.particle-0 {
  width: 10px;
  height: 10px;
  top: 20%;
  left: 10%;
  animation: float 15s infinite ease-in-out;
}

.particle-1 {
  width: 15px;
  height: 15px;
  top: 70%;
  left: 20%;
  animation: float 20s infinite ease-in-out;
}

.particle-2 {
  width: 8px;
  height: 8px;
  top: 40%;
  left: 50%;
  animation: float 18s infinite ease-in-out;
}

.particle-3 {
  width: 12px;
  height: 12px;
  top: 30%;
  left: 80%;
  animation: float 22s infinite ease-in-out;
}

.particle-4 {
  width: 6px;
  height: 6px;
  top: 80%;
  left: 85%;
  animation: float 25s infinite ease-in-out;
}

@keyframes float {
  0% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(20px, 20px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.banner-content {
  position: relative;
  z-index: 4;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 2rem;
  color: white;
}

.banner-main {
  display: flex;
  justify-content: space-between;
  flex: 1;
}

.user-profile {
  display: flex;
  align-items: center;
}

.user-avatar-wrapper {
  position: relative;
  margin-right: 2rem;
}

.user-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  background-color: #f3f4f6;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #359e04;
  color: white;
  font-size: 3rem;
  font-weight: 600;
}

.avatar-status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 5;
}

.verification-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.verification-badge.verified {
  background-color: #10b981;
  color: white;
}

.verification-badge.pending {
  background-color: #f59e0b;
  color: white;
}

.verification-badge.rejected {
  background-color: #ef4444;
  color: white;
}

.verification-badge.unverified {
  background-color: rgba(255, 255, 255, 0.9);
  color: #6b7280;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-greeting {
  margin-bottom: 0.5rem;
}

.greeting {
  font-size: 1.25rem;
  font-weight: 500;
  opacity: 0.9;
}

.user-name {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.25rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-username {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 0.5rem;
  font-style: italic;
}

.user-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 0.75rem;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
}

.meta-icon {
  margin-right: 0.5rem;
  opacity: 0.9;
}

.user-contact {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
}

.contact-icon {
  margin-right: 0.5rem;
  opacity: 0.9;
}

.banner-stats {
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  min-width: 300px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stats-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.view-all-link {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: color 0.2s ease;
}

.view-all-link:hover {
  color: white;
  text-decoration: underline;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
  background-color: rgba(255, 255, 255, 0.2);
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.stat-icon:hover {
  transform: scale(1.1);
}

.stat-icon.saved {
  color: #ef4444;
}

.stat-icon.bookings {
  color: #10b981;
}

.stat-icon.notifications {
  color: #3b82f6;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 0.25rem;
}

/* Responsive styles */
@media (max-width: 991px) {
  .enhanced-dashboard-banner {
    border-radius: 12px;
  }
}

@media (max-width: 768px) {
  .enhanced-dashboard-banner {
    min-height: auto;
    border-radius: 12px;
  }

  .banner-content {
    padding: 1.5rem;
  }

  .user-profile {
    flex-direction: column;
    text-align: center;
  }

  .user-avatar-wrapper {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }

  .user-avatar {
    width: 100px;
    height: 100px;
    margin: 0 auto;
  }
}
