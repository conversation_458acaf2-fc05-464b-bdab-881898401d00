/* Fix Banner Content Structure
 * This file ensures the banner content structure is correct and the footer is displayed
 */

/* Fix the overall banner structure */
.enhanced-dashboard-banner {
  display: flex !important;
  flex-direction: column !important;
  background-color: #359e04 !important; /* Ensure the background color is correct */
}

/* Fix the banner content structure */
.enhanced-dashboard-banner .banner-content {
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  height: auto !important;
  min-height: 320px !important;
  flex: 1 !important;
  padding-bottom: 4rem !important; /* Add extra padding at the bottom to ensure footer is visible */
}

/* Ensure the banner main takes up appropriate space */
.enhanced-dashboard-banner .banner-main {
  flex: 1 0 auto !important; /* Grow but don't shrink */
  margin-bottom: 2rem !important; /* Add space before the footer */
}

/* Ensure the banner footer is at the bottom of the content */
.enhanced-dashboard-banner .banner-footer {
  flex: 0 0 auto !important; /* Don't grow or shrink */
  position: relative !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  padding-bottom: 1rem !important; /* Add padding at the bottom */
  margin-top: 2rem !important; /* Add margin at the top */
  border-top: 1px solid rgba(255, 255, 255, 0.2) !important; /* Add border at the top */
}

/* Fix for the quick action buttons */
.enhanced-dashboard-banner .quick-action-button {
  pointer-events: auto !important; /* Ensure clicks work */
  cursor: pointer !important;
}

/* Fix for the button icons */
.enhanced-dashboard-banner .button-icon {
  display: inline-block !important;
  margin-right: 0.5rem !important;
}

/* Fix for any potential z-index issues */
.enhanced-dashboard-banner {
  z-index: 1 !important; /* Ensure the banner is above other elements */
}

.enhanced-dashboard-banner .banner-content {
  z-index: 2 !important; /* Ensure the content is above the banner */
}

.enhanced-dashboard-banner .banner-main {
  z-index: 3 !important; /* Ensure the main content is above the banner content */
}

.enhanced-dashboard-banner .banner-footer {
  z-index: 4 !important; /* Ensure the footer is above the main content */
}

.enhanced-dashboard-banner .quick-actions {
  z-index: 5 !important; /* Ensure the quick actions are above the footer */
}

.enhanced-dashboard-banner .quick-action-button {
  z-index: 6 !important; /* Ensure the buttons are above the quick actions */
}

/* Fix for any potential overflow issues */
.enhanced-dashboard-banner {
  overflow: visible !important; /* Ensure content isn't clipped */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-dashboard-banner .banner-content {
    padding: 1.5rem !important;
  }

  .enhanced-dashboard-banner .banner-footer {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
}
