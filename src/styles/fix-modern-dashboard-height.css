/* Fix Modern Dashboard Height
 * This file fixes the excessive height in the modern dashboard wrapper
 * by overriding min-height properties and ensuring content-based heights
 */

/* Fix for the dashboard-wrapper to prevent excessive height */
.dashboard-wrapper {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  margin-top: 52px !important; /* Match header height */
  padding-top: 0 !important;
}

/* Fix for the modern-main-content to prevent excessive height */
.modern-main-content {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  padding: 1.5rem !important;
}

/* Fix for the main-content-wrapper to prevent excessive height */
.main-content-wrapper {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
  overflow-y: visible !important;
}

/* Fix for the dashboard-content to prevent excessive height */
.dashboard-content {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
}

/* Fix for the dashboard-content to prevent excessive height */
.dashboard-content {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
}

/* Fix for the modern-main-content-wrapper to prevent excessive height */
.modern-main-content-wrapper {
  min-height: 0 !important; /* Firefox compatible */
  height: auto !important;
}

/* Fix for the container-fluid to have appropriate padding */
.container-fluid {
  padding: 1.5rem !important;
}

/* Fix for the modern-loading-container to remove fixed height */
.modern-loading-container {
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
  padding: 1.5rem 0 !important; /* Reduced padding */
}

/* Fix for the modern-empty-state to have appropriate padding */
.modern-empty-state {
  padding: 1.5rem 0 !important;
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
}

/* Fix for the modern-loading to have appropriate padding */
.modern-loading {
  padding: 1.5rem 0 !important;
  height: auto !important;
  min-height: 0 !important; /* Firefox compatible */
}

/* Fix for the modern-card to have appropriate margin */
.modern-card {
  margin-bottom: 1rem !important;
}

/* Fix for the last modern-card to have no margin */
.modern-card:last-child {
  margin-bottom: 0 !important;
}

/* Fix for the modern-section-header to have appropriate margin */
.modern-section-header {
  margin-bottom: 1.5rem !important;
}

/* Fix for the modern-section-header-large to have appropriate margin */
.modern-section-header-large {
  margin-bottom: 1.5rem !important;
  margin-top: 0 !important;
}

/* Fix for the modern-stats-grid to have appropriate margin */
.modern-stats-grid {
  margin-bottom: 1.5rem !important;
}

/* Fix for the modern-content-grid to have appropriate margin */
.modern-content-grid {
  margin-bottom: 0 !important;
}

/* Fix for the dashboard-section to have appropriate margin */
.dashboard-section {
  margin-bottom: 1.5rem !important;
}

/* Fix for the last dashboard-section to have no margin */
.dashboard-section:last-child {
  margin-bottom: 0 !important;
}

/* Fix for the enhanced-dashboard-banner to have appropriate margin */
.enhanced-dashboard-banner {
  margin-bottom: 1.5rem !important;
}

/* Fix for media queries */
@media (max-width: 991px) {
  .modern-dashboard-wrapper {
    min-height: 0 !important; /* Firefox compatible */
    height: auto !important;
  }
}

@media (max-width: 768px) {
  .modern-dashboard-wrapper {
    min-height: 0 !important; /* Firefox compatible */
    height: auto !important;
  }
}
