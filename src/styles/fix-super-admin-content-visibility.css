/* Fix Super Admin Content Visibility
 * This file ensures that the super admin dashboard content is visible
 * by fixing z-index issues and ensuring proper display properties
 */

/* Ensure the dashboard content is visible */
.dashboard-content {
  display: flex !important;
  position: relative !important;
  z-index: 1 !important;
  min-height: calc(100vh - 60px) !important; /* Account for header height */
  width: 100% !important;
}

/* Ensure the main content wrapper is visible */
.main-content-wrapper {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  position: relative !important;
  z-index: 10 !important; /* Higher z-index to ensure visibility */
  overflow: visible !important; /* Ensure content isn't clipped */
  height: auto !important; /* Allow content to determine height */
  min-height: calc(100vh - 60px) !important; /* Account for header height */
}

/* Ensure the modern-main-content is visible */
.modern-main-content {
  display: block !important;
  position: relative !important;
  z-index: 15 !important; /* Even higher z-index */
  visibility: visible !important; /* Explicitly set visibility */
  opacity: 1 !important; /* Ensure full opacity */
  height: auto !important; /* Allow content to determine height */
  overflow: visible !important; /* Ensure content isn't clipped */
}

/* Style for the super-admin-content-container */
.super-admin-content-container {
  display: block !important;
  width: 100% !important;
  position: relative !important;
  z-index: 20 !important; /* Higher z-index to ensure visibility */
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 200px !important; /* Minimum height to ensure visibility */
}

/* Ensure the container-fluid is visible */
.container-fluid {
  display: block !important;
  position: relative !important;
  z-index: 20 !important; /* Higher z-index than parent */
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure all dashboard components are visible */
.modern-stats-grid,
.modern-content-grid,
.modern-quick-links,
.modern-section-header,
.modern-card,
.modern-notification,
.modern-breadcrumb {
  display: block !important;
  position: relative !important;
  z-index: 25 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Fix for any potential overflow issues */
.dashboard-wrapper {
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

/* Ensure the sidebar doesn't overlap content on larger screens */
@media (min-width: 992px) {
  .modern-sidebar {
    z-index: 900 !important;
  }

  .main-content-wrapper {
    margin-left: 280px !important;
    width: calc(100% - 280px) !important;
  }
}

/* Mobile adjustments */
@media (max-width: 991px) {
  .modern-sidebar {
    z-index: 1000 !important; /* Higher z-index on mobile */
  }

  .main-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* Debug styles have been removed now that content is visible */
