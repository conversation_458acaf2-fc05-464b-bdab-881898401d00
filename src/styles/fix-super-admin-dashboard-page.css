/* Fix Super Admin Dashboard Page
 * This file provides a direct fix for the Super Admin Dashboard page
 * by targeting the specific page structure
 */

/* Target the specific dashboard page */
.super-admin-dashboard-page {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Target the modern-main-content in the dashboard page */
.super-admin-dashboard-page .modern-main-content,
.super-admin-dashboard-page .modern-main-content.super-admin-dashboard {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
}

/* Target the enhanced dashboard banner in the dashboard page */
.super-admin-dashboard-page .enhanced-dashboard-banner,
.super-admin-dashboard-page .enhanced-dashboard-banner.super-admin-banner {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 100% !important;
  max-width: 100% !important;
  margin-top: 10px !important;
  margin-bottom: 2rem !important;
  border-radius: 16px !important;
}

/* Target the stats grid in the dashboard page */
.super-admin-dashboard-page .modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Target the content grid in the dashboard page */
.super-admin-dashboard-page .modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Target the section header in the dashboard page */
.super-admin-dashboard-page .modern-section-header {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  margin-bottom: 1.5rem !important;
  padding: 0 !important;
  background-color: transparent !important;
  width: 100% !important;
}

/* Target the card in the dashboard page */
.super-admin-dashboard-page .modern-card {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  margin-bottom: 1.5rem !important;
  width: 100% !important;
}

/* Target the card header in the dashboard page */
.super-admin-dashboard-page .modern-card-header {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  padding: 1rem 1.5rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  background-color: transparent !important;
}

/* Target the card body in the dashboard page */
.super-admin-dashboard-page .modern-card-body {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  padding: 1.5rem !important;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .super-admin-dashboard-page .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .super-admin-dashboard-page .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
  
  .super-admin-dashboard-page .modern-main-content,
  .super-admin-dashboard-page .modern-main-content.super-admin-dashboard {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .super-admin-dashboard-page .modern-main-content,
  .super-admin-dashboard-page .modern-main-content.super-admin-dashboard {
    width: 100% !important;
  }
  
  .super-admin-dashboard-page .enhanced-dashboard-banner,
  .super-admin-dashboard-page .enhanced-dashboard-banner.super-admin-banner {
    border-radius: 0 !important;
  }
}
