/* Fix Super Admin Display Final
 * This file provides a final fix for display issues in the Super Admin Dashboard
 * by using !important to override any conflicting styles
 */

/* ===== RESET AND OVERRIDE CONFLICTING STYLES ===== */

/* Reset any hidden or invisible elements */
.super-admin-dashboard *,
.super-admin-dashboard-page *,
.modern-main-content * {
  visibility: visible !important;
  opacity: 1 !important;
}

/* Override any display: none that might be hiding content */
.super-admin-dashboard .modern-stats-grid,
.super-admin-dashboard .modern-content-grid,
.super-admin-dashboard .enhanced-stat-card,
.super-admin-dashboard .modern-card,
.super-admin-dashboard .modern-chart-card,
.super-admin-dashboard .modern-activity-card,
.super-admin-dashboard .container-fluid,
.super-admin-dashboard .main-content-area,
.super-admin-dashboard .sidebar-content {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure grid containers use proper display */
.super-admin-dashboard .modern-stats-grid {
  display: grid !important;
}

.super-admin-dashboard .modern-content-grid {
  display: grid !important;
}

.super-admin-dashboard .main-content-area,
.super-admin-dashboard .sidebar-content {
  display: flex !important;
}

/* Force display of all elements */
.dashboard-wrapper,
.dashboard-content,
.main-content-wrapper,
.super-admin-content-container,
.modern-main-content,
.container-fluid,
.modern-stats-grid,
.modern-content-grid,
.modern-section-header,
.modern-card,
.modern-notification,
.modern-breadcrumb,
.dashboard-footer {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force proper layout */
.dashboard-wrapper {
  display: flex !important;
  flex-direction: column !important;
}

.dashboard-content {
  display: flex !important;
  flex: 1 !important;
}

.main-content-wrapper {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.super-admin-content-container {
  display: block !important;
  width: 100% !important;
}

/* Force proper positioning */
.main-content-wrapper {
  position: relative !important;
  margin-left: 280px !important;
  width: calc(100% - 280px) !important;
}

/* Force proper margins */
.modern-main-content {
  margin: 0 auto !important;
}

/* Force proper width */
.modern-main-content {
  width: 90% !important;
  max-width: 1800px !important;
}

.container-fluid {
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
}

/* Force proper padding */
.container-fluid {
  padding: 1.5rem !important;
}

/* Force proper height */
.main-content-wrapper {
  height: auto !important;
  min-height: calc(100vh - 70px) !important;
}

/* Force proper z-index */
.header {
  z-index: 1050 !important;
}

.modern-sidebar {
  z-index: 1040 !important;
}

.main-content-wrapper {
  z-index: 1 !important;
}

/* ===== GRID LAYOUT FIXES ===== */

/* Ensure the modern stats grid is properly displayed */
.modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure the modern content grid is properly displayed */
.modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure the main content area is properly displayed */
.main-content-area {
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure the sidebar content is properly displayed */
.sidebar-content {
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* ===== CARD COMPONENT FIXES ===== */

/* Ensure all card components are visible */
.enhanced-stat-card,
.enhanced-stat-card-link,
.enhanced-stat-card-inner,
.modern-card,
.modern-chart-card,
.modern-activity-card {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Ensure stat cards have proper dimensions */
.enhanced-stat-card,
.enhanced-stat-card-link {
  height: auto !important;
  min-height: 150px !important;
  width: 100% !important;
}

/* Fix for enhanced stat card inner container */
.enhanced-stat-card-inner {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  min-height: 150px !important;
}

/* ===== SPECIFIC COMPONENT FIXES ===== */

/* Ensure activity grid is visible */
.activity-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 1.5rem !important;
  margin-top: 1.5rem !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure quick links are visible */
.modern-quick-links {
  display: block !important;
  width: 100% !important;
  margin-top: 2rem !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.modern-quick-links-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force proper overflow */
.dashboard-wrapper {
  overflow-x: hidden !important;
}

.main-content-wrapper {
  overflow-x: hidden !important;
  overflow-y: visible !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1200px) {
  .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .modern-content-grid {
    grid-template-columns: 1fr !important;
  }

  .activity-grid {
    grid-template-columns: 1fr !important;
  }

  .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 991px) {
  .main-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .modern-main-content,
  .container-fluid {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }

  .modern-quick-links-grid {
    grid-template-columns: 1fr !important;
  }

  .modern-main-content,
  .container-fluid {
    width: 100% !important;
    padding: 1.5rem !important;
  }
}
