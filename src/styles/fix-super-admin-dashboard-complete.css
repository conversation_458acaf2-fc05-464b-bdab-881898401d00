/* Fix Super Admin Dashboard Complete
 * This file provides a comprehensive solution to fix all width issues in the super admin dashboard
 * by using highly specific selectors with !important to override any conflicting styles
 */

/* ===== CONTAINER HIERARCHY FIXES ===== */

/* Target the dashboard-wrapper directly */
body .dashboard-wrapper {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Target the dashboard-content directly */
body .dashboard-wrapper .dashboard-content {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  position: relative !important;
}

/* Target the main-content-wrapper directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper {
  width: calc(100% - 280px) !important;
  margin-left: 280px !important;
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Target the super-admin-content-container directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  align-self: stretch !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ===== DASHBOARD CONTENT FIXES ===== */

/* Target the modern-main-content with super-admin-dashboard class directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Target the enhanced-dashboard-banner inside super-admin-dashboard directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .enhanced-dashboard-banner {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 0 1.5rem 0 !important;
  border-radius: 12px !important;
  align-self: stretch !important;
}

/* Target the container-fluid inside super-admin-dashboard directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* ===== GRID LAYOUT FIXES ===== */

/* Target the modern-stats-grid inside super-admin-dashboard directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-stats-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Target the modern-content-grid inside super-admin-dashboard directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Target the main-content-area inside super-admin-dashboard directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid .main-content-area {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Target the sidebar-content inside super-admin-dashboard directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid .sidebar-content {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Target the modern-quick-links-grid inside super-admin-dashboard directly */
body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-quick-links-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1200px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 991px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper {
    width: 100% !important;
    margin-left: 0 !important;
  }
}

@media (max-width: 768px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }

  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid {
    padding: 1rem !important;
  }
}

@media (max-width: 576px) {
  body .dashboard-wrapper .dashboard-content #main-content.main-content-wrapper .super-admin-content-container .super-admin-dashboard.modern-main-content .container-fluid .modern-quick-links-grid {
    grid-template-columns: 1fr !important;
  }
}
