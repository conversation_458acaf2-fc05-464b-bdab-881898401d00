/* Enhanced Dashboard Banner Styles Part 2
 * This file contains additional styles for the EnhancedDashboardBanner component
 */

.banner-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-completion {
  width: 50%; /* Changed from flex: 1 to a fixed width of 50% */
  margin-right: 2rem;
}

.completion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.completion-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
}

.completion-percentage {
  font-size: 0.875rem;
  font-weight: 700;
}

.completion-progress-bar {
  height: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.completion-progress {
  height: 100%;
  background-color: white;
  border-radius: 4px;
  transition: width 1s ease-out;
}

.quick-actions {
  display: flex;
  gap: 1rem;
}

.quick-action-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  text-decoration: none;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(5px);
}

.quick-action-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.button-icon {
  margin-right: 0.5rem;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .banner-main {
    flex-direction: column;
    gap: 2rem;
  }

  .banner-stats {
    width: 100%;
  }
}

@media (max-width: 991px) {
  .enhanced-dashboard-banner {
    width: 95%;
    border-radius: 12px;
  }
}

@media (max-width: 768px) {
  .enhanced-dashboard-banner {
    min-height: auto;
    width: 95%;
    border-radius: 12px;
  }

  .banner-content {
    padding: 1.5rem;
  }

  .user-profile {
    flex-direction: column;
    text-align: center;
  }

  .user-avatar-wrapper {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }

  .user-avatar {
    width: 100px;
    height: 100px;
    margin: 0 auto;
  }

  .user-meta, .user-contact {
    justify-content: center;
  }

  .banner-footer {
    flex-direction: column;
    gap: 1.5rem;
  }

  .profile-completion {
    width: 70%; /* Changed from 100% to 70% for smaller screens */
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .quick-actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .stat-content {
    align-items: flex-start;
  }

  .user-name {
    font-size: 1.5rem;
  }

  .greeting {
    font-size: 1rem;
  }

  .user-meta, .user-contact {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }
}
