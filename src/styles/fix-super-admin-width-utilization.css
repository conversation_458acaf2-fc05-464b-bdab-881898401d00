/* Fix Super Admin Width Utilization
 * This file ensures that the super admin dashboard properly utilizes the full width
 * of the available space, eliminating empty space on the right side
 */

/* Ensure the main content takes full width of the available space */
.super-admin-dashboard.modern-main-content {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Ensure the container has full width */
.super-admin-dashboard .container-fluid {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* Ensure the stats grid uses full width and has proper column distribution */
.super-admin-dashboard .modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Ensure the content grid uses full width */
.super-admin-dashboard .modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Ensure the main content area uses full width */
.super-admin-dashboard .main-content-area {
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

/* Ensure the sidebar content uses full width */
.super-admin-dashboard .sidebar-content {
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

/* Ensure the quick links grid uses full width */
.super-admin-dashboard .modern-quick-links-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  width: 100% !important;
}

/* Ensure the dashboard banner uses full width */
.super-admin-dashboard .enhanced-dashboard-banner {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 0 1.5rem 0 !important;
  border-radius: 12px !important;
}

/* Ensure the main content wrapper takes full width */
.main-content-wrapper {
  width: calc(100% - 280px) !important;
  margin-left: 280px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Ensure the super admin content container takes full width */
.super-admin-dashboard.modern-main-content .super-admin-content-container {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  align-self: stretch !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .super-admin-dashboard .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .super-admin-dashboard .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  .super-admin-dashboard .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }

  .super-admin-dashboard .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .super-admin-dashboard.modern-main-content,
  .super-admin-dashboard .container-fluid {
    width: 100% !important;
    padding: 1rem !important;
  }
}

@media (max-width: 576px) {
  .super-admin-dashboard .modern-quick-links-grid {
    grid-template-columns: 1fr !important;
  }
}
