/* Fix Dashboard Wrapper Min Height
 * This file specifically targets the min-height: 100vh property in the dashboard-wrapper class
 * which is causing excessive empty space in the super admin dashboard
 */

/* Override the min-height: 100vh property in dashboard.css */
.dashboard-wrapper {
  min-height: 0 !important;
  height: auto !important;
}

/* Ensure the dashboard-content also has appropriate height */
.dashboard-content {
  min-height: 0 !important;
  height: auto !important;
}

/* Ensure the main-content-wrapper also has appropriate height */
.main-content-wrapper {
  min-height: 0 !important;
  height: auto !important;
}

/* Ensure the modern-main-content also has appropriate height */
.modern-main-content {
  min-height: 0 !important;
  height: auto !important;
}

/* Ensure the container-fluid also has appropriate height */
.container-fluid {
  min-height: 0 !important;
  height: auto !important;
}

/* Ensure the content-table-card has no margin-bottom */
.content-table-card {
  margin-bottom: 0 !important;
}

/* Ensure the modern-card-body has appropriate padding */
.modern-card-body {
  padding-bottom: 0.5rem !important;
}

/* Ensure the modern-table-container has appropriate height */
.modern-table-container {
  height: auto !important;
  min-height: 0 !important;
  max-height: none !important;
  overflow-y: visible !important;
}
