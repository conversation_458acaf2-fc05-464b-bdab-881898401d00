/* Fix Super Admin Dashboard Width
 * This file provides a comprehensive fix for the super admin dashboard width issues
 * by directly targeting all container elements with high specificity selectors
 */

/* Target the main-content-wrapper directly */
#main-content.main-content-wrapper {
  width: calc(100% - 280px) !important;
  margin-left: 280px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  padding: 0 !important;
}

/* Target the super-admin-content-container directly */
#main-content.main-content-wrapper .super-admin-content-container {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  align-self: stretch !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Target the modern-main-content with super-admin-dashboard class directly */
.super-admin-dashboard.modern-main-content {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/* Target the container-fluid inside super-admin-dashboard directly */
.super-admin-dashboard.modern-main-content .container-fluid {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* Target the modern-stats-grid inside super-admin-dashboard directly */
.super-admin-dashboard.modern-main-content .modern-stats-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Target the modern-content-grid inside super-admin-dashboard directly */
.super-admin-dashboard.modern-main-content .modern-content-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Target the main-content-area inside super-admin-dashboard directly */
.super-admin-dashboard.modern-main-content .main-content-area {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Target the sidebar-content inside super-admin-dashboard directly */
.super-admin-dashboard.modern-main-content .sidebar-content {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Target the modern-quick-links-grid inside super-admin-dashboard directly */
.super-admin-dashboard.modern-main-content .modern-quick-links-grid {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
}

/* Target the enhanced-dashboard-banner inside super-admin-dashboard directly */
.super-admin-dashboard.modern-main-content .enhanced-dashboard-banner {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 0 1.5rem 0 !important;
  border-radius: 12px !important;
}

/* Override any max-width constraints on container elements */
.super-admin-dashboard.modern-main-content *[class*="container"],
.super-admin-dashboard.modern-main-content *[class*="wrapper"],
.super-admin-dashboard.modern-main-content *[class*="content"],
.super-admin-dashboard.modern-main-content *[class*="section"],
.super-admin-dashboard.modern-main-content *[class*="grid"] {
  max-width: none !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .super-admin-dashboard.modern-main-content .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .super-admin-dashboard.modern-main-content .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  #main-content.main-content-wrapper {
    width: 100% !important;
    margin-left: 0 !important;
  }

  .super-admin-dashboard.modern-main-content .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }

  .super-admin-dashboard.modern-main-content .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .super-admin-dashboard.modern-main-content,
  .super-admin-dashboard.modern-main-content .container-fluid {
    width: 100% !important;
    padding: 1rem !important;
  }
}

@media (max-width: 576px) {
  .super-admin-dashboard.modern-main-content .modern-quick-links-grid {
    grid-template-columns: 1fr !important;
  }
}
