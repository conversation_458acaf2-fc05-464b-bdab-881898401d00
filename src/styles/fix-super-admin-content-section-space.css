/* Fix Super Admin Content Section Space
 * This file specifically targets the excessive empty space in the super admin content section
 * by ensuring all height-related properties are set to auto or 0
 */

/* Fix for the dashboard wrapper and content */
html body .dashboard-wrapper,
body .dashboard-wrapper,
.dashboard-wrapper {
  min-height: 0 !important;
  height: auto !important;
  display: flex !important;
  flex-direction: column !important;
}

html body .dashboard-content,
body .dashboard-content,
.dashboard-content {
  min-height: 0 !important;
  height: auto !important;
  flex: 0 !important;
}

/* Fix for the main content wrapper */
html body .main-content-wrapper,
body .main-content-wrapper,
.main-content-wrapper {
  min-height: 0 !important;
  height: auto !important;
  flex: 0 !important;
  overflow-y: visible !important;
}

/* Fix for the modern main content */
html body .modern-main-content,
body .modern-main-content,
.modern-main-content {
  min-height: 0 !important;
  height: auto !important;
  padding: 1.5rem !important;
}

/* Fix for the container fluid */
html body .container-fluid,
body .container-fluid,
.container-fluid {
  min-height: 0 !important;
  height: auto !important;
  padding: 1.5rem !important;
  padding-bottom: 1rem !important;
}

/* Fix for the content table card */
html body .content-table-card,
body .content-table-card,
.content-table-card {
  margin-bottom: 0 !important;
}

/* Fix for the modern card body */
html body .modern-card-body,
body .modern-card-body,
.modern-card-body {
  padding: 1rem !important;
  padding-bottom: 0.5rem !important;
}

/* Fix for the modern table container */
html body .modern-table-container,
body .modern-table-container,
.modern-table-container {
  height: auto !important;
  min-height: 0 !important;
  max-height: none !important;
  overflow-y: visible !important;
}

/* Fix for the modern table */
html body .modern-table,
body .modern-table,
.modern-table {
  height: auto !important;
  min-height: 0 !important;
}

/* Fix for the modern table body */
html body .modern-table-body,
body .modern-table-body,
.modern-table-body {
  height: auto !important;
  min-height: 0 !important;
}

/* Fix for the modern loading container */
html body .modern-loading-container,
body .modern-loading-container,
.modern-loading-container {
  height: auto !important;
  min-height: 0 !important;
  padding: 1.5rem 0 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Fix for the modern empty state */
html body .modern-empty-state,
body .modern-empty-state,
.modern-empty-state {
  padding: 1.5rem 0 !important;
  height: auto !important;
  min-height: 0 !important;
}

/* Fix for the modern section header */
html body .modern-section-header,
body .modern-section-header,
.modern-section-header {
  margin-bottom: 1.5rem !important;
}

/* Fix for the modern pagination */
html body .modern-pagination,
body .modern-pagination,
.modern-pagination {
  margin-top: 0.5rem !important;
  margin-bottom: 0 !important;
}

/* Fix for any row elements that might have margin-bottom */
html body .row,
body .row,
.row {
  margin-bottom: 1rem !important;
}

/* Fix for the last row to have no bottom margin */
html body .row:last-child,
body .row:last-child,
.row:last-child {
  margin-bottom: 0 !important;
}

/* Fix for the modern content grid */
html body .modern-content-grid,
body .modern-content-grid,
.modern-content-grid {
  margin-bottom: 0 !important;
}

/* Fix for the modern filter pills */
html body .modern-filter-pills,
body .modern-filter-pills,
.modern-filter-pills {
  margin-bottom: 0.5rem !important;
}

/* Fix for the modern card */
html body .modern-card,
body .modern-card,
.modern-card {
  margin-bottom: 1rem !important;
}

/* Fix for the last modern card */
html body .modern-card:last-child,
body .modern-card:last-child,
.modern-card:last-child {
  margin-bottom: 0 !important;
}
