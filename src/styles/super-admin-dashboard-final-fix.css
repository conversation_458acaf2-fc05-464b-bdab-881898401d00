/* Super Admin Dashboard Final Fix
 * This file provides a final fix for the Super Admin Dashboard
 * by directly targeting the specific elements in the dashboard page
 */

/* ===== RESET ALL DISPLAY PROPERTIES ===== */

/* Reset display properties for all elements */
.super-admin-dashboard-page,
.super-admin-dashboard-page *,
.modern-main-content.super-admin-dashboard,
.modern-main-content.super-admin-dashboard * {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* ===== SPECIFIC ELEMENT FIXES ===== */

/* Fix for the enhanced dashboard banner */
.super-admin-dashboard-page .enhanced-dashboard-banner,
.super-admin-dashboard-page .enhanced-dashboard-banner.super-admin-banner {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 100% !important;
  max-width: 100% !important;
  margin-top: 10px !important;
  margin-bottom: 2rem !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Fix for the stats grid */
.super-admin-dashboard-page .modern-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Fix for the content grid */
.super-admin-dashboard-page .modern-content-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Fix for the section header */
.super-admin-dashboard-page .modern-section-header {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  margin-bottom: 1.5rem !important;
  padding: 0 !important;
  background-color: transparent !important;
  width: 100% !important;
}

/* Fix for the card */
.super-admin-dashboard-page .modern-card {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  margin-bottom: 1.5rem !important;
  width: 100% !important;
}

/* Fix for the card header */
.super-admin-dashboard-page .modern-card-header {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  padding: 1rem 1.5rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  background-color: transparent !important;
}

/* Fix for the card body */
.super-admin-dashboard-page .modern-card-body {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  padding: 1.5rem !important;
}

/* Fix for the container fluid */
.super-admin-dashboard-page .container-fluid {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 90% !important;
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 1.5rem !important;
  box-sizing: border-box !important;
}

/* Fix for the quick links */
.super-admin-dashboard-page .modern-quick-links {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 100% !important;
  margin-bottom: 2rem !important;
}

/* Fix for the quick links grid */
.super-admin-dashboard-page .modern-quick-links-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  width: 100% !important;
}

/* Fix for the quick link card */
.super-admin-dashboard-page .modern-quick-link-card {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  padding: 1.5rem !important;
  width: 100% !important;
  text-decoration: none !important;
  color: inherit !important;
}

/* Fix for the quick link icon */
.super-admin-dashboard-page .quick-link-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: 12px !important;
  margin-right: 1rem !important;
}

/* Fix for the quick link content */
.super-admin-dashboard-page .quick-link-content {
  display: block !important;
  flex: 1 !important;
}

/* Fix for the quick link title */
.super-admin-dashboard-page .quick-link-title {
  display: block !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: var(--gray-900) !important;
  margin: 0 !important;
  margin-bottom: 0.25rem !important;
}

/* Fix for the quick link description */
.super-admin-dashboard-page .quick-link-description {
  display: block !important;
  font-size: 0.875rem !important;
  color: var(--gray-600) !important;
  margin: 0 !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 991px) {
  .super-admin-dashboard-page .modern-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .super-admin-dashboard-page .modern-content-grid {
    grid-template-columns: 1fr !important;
  }
  
  .super-admin-dashboard-page .modern-quick-links-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .super-admin-dashboard-page .container-fluid {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .super-admin-dashboard-page .modern-stats-grid {
    grid-template-columns: 1fr !important;
  }
  
  .super-admin-dashboard-page .modern-quick-links-grid {
    grid-template-columns: 1fr !important;
  }
  
  .super-admin-dashboard-page .container-fluid {
    width: 100% !important;
  }
  
  .super-admin-dashboard-page .enhanced-dashboard-banner,
  .super-admin-dashboard-page .enhanced-dashboard-banner.super-admin-banner {
    border-radius: 0 !important;
  }
}
