'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * A component that handles redirect objects and prevents them from being rendered directly
 * This is a last resort solution for the "Objects are not valid as a React child" error
 */
export default function RedirectHandler({ children }) {
  const router = useRouter();

  // Process children to handle redirect objects
  const processChildren = (child) => {
    // Check if child is a redirect object
    if (typeof child === 'object' && child !== null && 'redirect' in child) {
      // Handle redirect in useEffect
      useEffect(() => {
        console.log('RedirectHandler: Redirecting to', child.redirect);
        router.push(child.redirect);
      }, []);

      // Return a placeholder instead of the redirect object
      return <div className="text-center p-4">Redirecting...</div>;
    }

    // If it's an array, process each item
    if (Array.isArray(child)) {
      return child.map((item, index) => (
        <React.Fragment key={index}>
          {processChildren(item)}
        </React.Fragment>
      ));
    }

    // If it's a React element with children, process its children
    if (React.isValidElement(child) && child.props.children) {
      return React.cloneElement(child, {
        children: processChildren(child.props.children)
      });
    }

    // Otherwise, return as is
    return child;
  };

  // Process and return the children
  return <>{processChildren(children)}</>;
}

/**
 * Utility function to safely render children that might contain redirect objects
 */
export function safeRender(children) {
  // Handle null or undefined
  if (children == null) {
    return null;
  }

  // Process children recursively
  const processChild = (child) => {
    // Handle direct redirect objects
    if (typeof child === 'object' && child !== null && 'redirect' in child) {
      return <div className="text-center p-4">Redirecting...</div>;
    }

    // Handle arrays with redirect objects
    if (Array.isArray(child)) {
      return child.map((item, index) => (
        <React.Fragment key={index}>
          {processChild(item)}
        </React.Fragment>
      ));
    }

    // If it's a React element with children, process its children
    if (React.isValidElement(child) && child.props.children) {
      return React.cloneElement(child, {
        children: processChild(child.props.children)
      });
    }

    // Otherwise, return as is
    return child;
  };

  return processChild(children);
}
