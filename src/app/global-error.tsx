'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationTriangle, faHome, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import Link from 'next/link';
import '@/styles/error-boundary.css';

/**
 * Global error component for handling fatal errors in the root layout
 * This is a simplified version without the full layout since the layout itself might be the source of the error
 */
export default function GlobalError({
  error,
  reset
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html lang="en">
      <head>
        <title>Something Went Wrong | FindaPro</title>
        <meta name="description" content="We encountered an error while processing your request" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossOrigin="anonymous" />
      </head>
      <body>
        <div className="container py-5">
          <div className="row justify-content-center">
            <div className="col-lg-8 text-center">
              <div className="error-image mb-4">
                <FontAwesomeIcon
                  icon={faExclamationTriangle}
                  className="text-warning error-icon-large"
                />
              </div>

              <h1 className="display-4 fw-bold mb-3">Critical Error</h1>

              <p className="lead text-muted mb-4">
                We're sorry, but a critical error occurred while loading the application.
                Our team has been notified and we're working to fix the issue.
              </p>

              <div className="d-flex flex-column flex-md-row justify-content-center gap-3 mb-5">
                <button
                  type="button"
                  onClick={() => reset()}
                  className="btn btn-primary btn-lg"
                >
                  <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
                  Try Again
                </button>

                <Link href="/" className="btn btn-outline-primary btn-lg">
                  <FontAwesomeIcon icon={faHome} className="me-2" />
                  Back to Home
                </Link>
              </div>

              {process.env.NODE_ENV === 'development' && (
                <div className="error-details p-4 bg-light rounded-3 text-start">
                  <h5 className="mb-3">Error Details</h5>
                  <p className="mb-0 text-danger">
                    {error?.message || 'An unexpected error occurred'}
                  </p>
                  {error?.stack && (
                    <pre className="mt-3 p-3 bg-dark text-light rounded small error-stack-trace">
                      {error.stack}
                    </pre>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
