import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import User from '@/models/User';
import dbConnect from '@/lib/mongoose';
import { verifyToken, getUserIdFromCookie } from '@/lib/auth-helpers';

export async function GET() {
  try {
    // Get user ID from cookie
    const userId = await getUserIdFromCookie();

    if (!userId) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await dbConnect();

    // Get user from database
    let user = await User.findById(userId);

    if (!user) {
      // In development mode, create a mock super admin user if it doesn't exist
      if (process.env.NODE_ENV === 'development' && userId === '507f1f77bcf86cd799439011') {
        console.log('Creating mock super admin user for development');
        user = {
          _id: userId,
          name: 'Super Admin',
          email: '<EMAIL>',
          username: 'superadmin',
          role: 'super_admin',
          profileImage: '/assets/img/default-avatar.png'
        };
      } else {
        // User no longer exists in the database
        const cookieStore = await cookies();
        cookieStore.delete('auth_token');
        cookieStore.delete('user_role');
        return NextResponse.json(
          { error: 'User not found' },
          { status: 401 }
        );
      }
    }

    // Return user data
    return NextResponse.json({
      id: user._id,
      name: user.name,
      email: user.email,
      username: user.username,
      role: user.role,
      profileImage: user.profileImage || `/api/avatar/default?letter=${user.name ? user.name.charAt(0) : 'U'}`
    });
  } catch (error) {
    console.error('Authentication check error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}