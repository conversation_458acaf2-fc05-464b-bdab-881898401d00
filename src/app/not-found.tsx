'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faSearch, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import MainLayout from '@/layouts/MainLayout';

export default function NotFound() {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/blog?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <MainLayout>
      <div className="not-found-page py-5">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8 text-center">
              <div className="error-image mb-4">
                <Image
                  src="/assets/img/404.png"
                  alt="Page not found"
                  width={400}
                  height={300}
                  className="img-fluid"
                />
              </div>

              <h1 className="display-4 fw-bold mb-3">Oops! Page Not Found</h1>

              <p className="lead text-muted mb-4">
                The page you are looking for might have been removed, had its name changed,
                or is temporarily unavailable.
              </p>

              <div className="d-flex flex-column flex-md-row justify-content-center gap-3 mb-5">
                <Link href="/" className="btn btn-primary btn-lg">
                  <FontAwesomeIcon icon={faHome} className="me-2" />
                  Back to Home
                </Link>

                <Link href="/blog" className="btn btn-outline-primary btn-lg">
                  <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
                  Go to Blog
                </Link>
              </div>

              <div className="search-section p-4 bg-light rounded-3">
                <h5 className="mb-3">Looking for something specific?</h5>

                <form onSubmit={handleSearch} className="row g-2 justify-content-center">
                  <div className="col-md-8">
                    <input
                      type="text"
                      className="form-control form-control-lg"
                      placeholder="Search our blog..."
                      aria-label="Search our blog"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div className="col-md-auto">
                    <button type="submit" className="btn btn-primary btn-lg w-100">
                      <FontAwesomeIcon icon={faSearch} className="me-2" />
                      Search
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
