'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import { FaSearch, FaCheck, FaTimes, FaEye, FaBuilding, FaFileAlt, FaIdCard } from 'react-icons/fa';

// Client component for KYB review page
export default function ReviewKYB() {
  const { user, loading: authLoading } = useAuth();
  const [kybSubmissions, setKybSubmissions] = useState([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('pending');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const submissionsPerPage = 10;

  useEffect(() => {
    if (authLoading) return;

    fetchKYBSubmissions();
  }, [authLoading, currentPage, statusFilter, searchTerm]);

  const fetchKYBSubmissions = async () => {
    setLoading(true);
    setError(null);

    try {
      // Build query parameters for API call
      const queryParams = new URLSearchParams({
        status: statusFilter !== 'all' ? statusFilter : '',
        page: currentPage,
        limit: submissionsPerPage,
        search: searchTerm
      }).toString();

      // Fetch KYB submissions from API
      // Use the simpler API endpoint for development
      const response = await fetch(`/api/kyb?${queryParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch KYB submissions');
      }

      const data = await response.json();

      // If API call is successful but we're still developing, use mock data
      // Remove this in production
      const mockSubmissions = [
          { id: 'kyb1', businessId: 'b1', businessName: 'Cafe Delight', ownerName: 'Jane Smith', ownerId: 'u2', registrationNumber: 'REG12345678', taxId: 'TAX12345678', status: 'verified', submittedAt: '2023-06-15T08:30:00Z', verifiedAt: '2023-06-16T10:15:00Z' },
          { id: 'kyb2', businessId: 'b2', businessName: 'Tech Solutions', ownerName: 'Robert Brown', ownerId: 'u7', registrationNumber: 'REG87654321', taxId: 'TAX87654321', status: 'pending', submittedAt: '2023-06-14T13:45:00Z' },
          { id: 'kyb3', businessId: 'b3', businessName: 'Fitness Hub', ownerName: 'Emily Davis', ownerId: 'u4', registrationNumber: 'REG23456789', taxId: 'TAX23456789', status: 'unverified', submittedAt: '2023-06-13T10:15:00Z' },
          { id: 'kyb4', businessId: 'b4', businessName: 'Green Gardens', ownerName: 'Michael Wilson', ownerId: 'u5', registrationNumber: 'REG34567890', taxId: 'TAX34567890', status: 'verified', submittedAt: '2023-06-12T15:20:00Z', verifiedAt: '2023-06-13T11:30:00Z' },
          { id: 'kyb5', businessId: 'b5', businessName: 'City Hotel', ownerName: 'Sarah Johnson', ownerId: 'u6', registrationNumber: 'REG45678901', taxId: 'TAX45678901', status: 'pending', submittedAt: '2023-06-11T09:10:00Z' },
          { id: 'kyb6', businessId: 'b6', businessName: 'Fresh Groceries', ownerName: 'David Miller', ownerId: 'u7', registrationNumber: 'REG56789012', taxId: 'TAX56789012', status: 'verified', submittedAt: '2023-06-10T14:30:00Z', verifiedAt: '2023-06-11T16:45:00Z' },
          { id: 'kyb7', businessId: 'b7', businessName: 'Elegant Styles', ownerName: 'Lisa Taylor', ownerId: 'u8', registrationNumber: 'REG67890123', taxId: 'TAX67890123', status: 'rejected', submittedAt: '2023-06-09T11:45:00Z', rejectionReason: 'Invalid business registration documents' },
          { id: 'kyb8', businessId: 'b8', businessName: 'Quick Repairs', ownerName: 'James Anderson', ownerId: 'u9', registrationNumber: 'REG78901234', taxId: 'TAX78901234', status: 'pending', submittedAt: '2023-06-08T16:20:00Z' },
          { id: 'kyb9', businessId: 'b9', businessName: 'Sunset Restaurant', ownerName: 'Jennifer White', ownerId: 'u10', registrationNumber: 'REG89012345', taxId: 'TAX89012345', status: 'verified', submittedAt: '2023-06-07T12:15:00Z', verifiedAt: '2023-06-08T14:30:00Z' },
          { id: 'kyb10', businessId: 'b10', businessName: 'Modern Furniture', ownerName: 'Thomas Harris', ownerId: 'u11', registrationNumber: 'REG90123456', taxId: 'TAX90123456', status: 'unverified', submittedAt: '2023-06-06T09:30:00Z' },
          { id: 'kyb11', businessId: 'b11', businessName: 'Wellness Spa', ownerName: 'Jessica Martin', ownerId: 'u12', registrationNumber: 'REG01234567', taxId: 'TAX01234567', status: 'verified', submittedAt: '2023-06-05T14:45:00Z', verifiedAt: '2023-06-06T16:20:00Z' },
          { id: 'kyb12', businessId: 'b12', businessName: 'Digital Marketing', ownerName: 'Robert Johnson', ownerId: 'u3', registrationNumber: 'REG12345670', taxId: 'TAX12345670', status: 'rejected', submittedAt: '2023-06-04T10:20:00Z', rejectionReason: 'Business address verification failed' }
        ];

        // Use API data if available, otherwise use mock data
        const submissions = data.businesses || mockSubmissions;
        const pagination = data.pagination || {
          total: mockSubmissions.length,
          pages: Math.ceil(mockSubmissions.length / submissionsPerPage)
        };

        setKybSubmissions(submissions);
        setFilteredSubmissions(submissions);
        setTotalPages(pagination.pages);

        // If using mock data, apply filters client-side
        if (!data.businesses) {
          applyFilters(mockSubmissions, searchTerm, statusFilter);
        }
      } catch (err) {
        console.error('Error fetching KYB submissions:', err);
        setError('Failed to load KYB submissions. Please try again later.');
      } finally {
        setLoading(false);
      }
  };

  // We're now handling filtering in the API call, but keep the applyFilters function
  // for client-side filtering when using mock data

  const applyFilters = (submissions, search, status) => {
    let filtered = [...submissions];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(submission =>
        submission.businessName.toLowerCase().includes(searchLower) ||
        submission.ownerName.toLowerCase().includes(searchLower) ||
        submission.registrationNumber.toLowerCase().includes(searchLower) ||
        submission.taxId.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (status !== 'all') {
      filtered = filtered.filter(submission => submission.status === status);
    }

    setFilteredSubmissions(filtered);
    setTotalPages(Math.ceil(filtered.length / submissionsPerPage));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    applyFilters(kybSubmissions, searchTerm, statusFilter);
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setCurrentPage(1);
  };

  const handleApproveKYB = async (submissionId) => {
    if (!confirm('Are you sure you want to approve this KYB submission?')) {
      return;
    }

    try {
      // Call API to approve KYB submission
      // Use the simpler API endpoint for development
      const response = await fetch(`/api/kyb/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'approve'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to approve KYB submission');
      }

      // Refresh KYB submissions
      fetchKYBSubmissions();
    } catch (err) {
      console.error('Error approving KYB submission:', err);
      alert(err.message || 'Failed to approve KYB submission. Please try again.');
    }
  };

  const handleRejectKYB = async (submissionId) => {
    const reason = prompt('Please enter a reason for rejection:');
    if (!reason) {
      return;
    }

    try {
      // Call API to reject KYB submission
      // Use the simpler API endpoint for development
      const response = await fetch(`/api/kyb/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'reject',
          reason: reason
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reject KYB submission');
      }

      // Refresh KYB submissions
      fetchKYBSubmissions();
    } catch (err) {
      console.error('Error rejecting KYB submission:', err);
      alert(err.message || 'Failed to reject KYB submission. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get current submissions for pagination
  const indexOfLastSubmission = currentPage * submissionsPerPage;
  const indexOfFirstSubmission = indexOfLastSubmission - submissionsPerPage;
  const currentSubmissions = filteredSubmissions.slice(indexOfFirstSubmission, indexOfLastSubmission);

  // Render function for the content
  const renderContent = () => {
    if (authLoading || loading) {
      return (
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading KYB submissions...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      );
    }

    return (
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Review KYB Submissions</h1>
        </div>

        {/* Filters */}
        <div className="card shadow mb-4">
          <div className="card-body">
            <div className="row align-items-center">
              <div className="col-md-6 mb-3 mb-md-0">
                <form onSubmit={handleSearch}>
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search by business name, owner, or registration number"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <button className="btn btn-primary" type="submit">
                      <FaSearch className="me-1" /> Search
                    </button>
                  </div>
                </form>
              </div>
              <div className="col-md-6">
                <div className="d-flex justify-content-md-end">
                  <select
                    className="form-select"
                    value={statusFilter}
                    onChange={handleStatusFilterChange}
                  >
                    <option value="all">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="verified">Verified</option>
                    <option value="unverified">Unverified</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* KYB Submissions Table */}
        <div className="card shadow mb-4">
          <div className="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 className="m-0 font-weight-bold text-primary">KYB Submissions</h6>
            <div className="badge bg-primary">
              {filteredSubmissions.filter(s => s.status === 'pending').length} Pending Reviews
            </div>
          </div>
          <div className="card-body">
            {currentSubmissions.length === 0 ? (
              <div className="text-center py-4">
                <p className="mb-0">No KYB submissions found matching your criteria.</p>
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-bordered" width="100%" cellSpacing="0">
                  <thead>
                    <tr>
                      <th>Business Name</th>
                      <th>Owner</th>
                      <th>Registration Number</th>
                      <th>Tax ID</th>
                      <th>Status</th>
                      <th>Submitted</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentSubmissions.map((submission) => (
                      <tr key={submission.id}>
                        <td>
                          <Link href={`/admin/manage-businesses/${submission.businessId}`} className="text-decoration-none">
                            {submission.businessName}
                          </Link>
                        </td>
                        <td>
                          <Link href={`/admin/manage-users/${submission.ownerId}`} className="text-decoration-none">
                            {submission.ownerName}
                          </Link>
                        </td>
                        <td>{submission.registrationNumber}</td>
                        <td>{submission.taxId}</td>
                        <td>
                          <span className={`badge ${
                            submission.status === 'verified' ? 'bg-success' :
                            submission.status === 'rejected' ? 'bg-danger' :
                            submission.status === 'pending' ? 'bg-warning text-dark' :
                            'bg-secondary'
                          }`}>
                            {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                          </span>
                          {submission.status === 'rejected' && submission.rejectionReason && (
                            <div className="small text-danger mt-1">
                              Reason: {submission.rejectionReason}
                            </div>
                          )}
                        </td>
                        <td>
                          {formatDate(submission.submittedAt)}
                          {submission.verifiedAt && (
                            <div className="small text-success mt-1">
                              Verified: {formatDate(submission.verifiedAt)}
                            </div>
                          )}
                        </td>
                        <td>
                          <div className="btn-group">
                            <Link href={`/admin/review-kyb/${submission.id}`} className="btn btn-sm btn-info me-1">
                              <FaEye /> View Documents
                            </Link>
                            {submission.status === 'pending' && (
                              <>
                                <button
                                  className="btn btn-sm btn-success me-1"
                                  onClick={() => handleApproveKYB(submission.id)}
                                >
                                  <FaCheck /> Approve
                                </button>
                                <button
                                  className="btn btn-sm btn-danger"
                                  onClick={() => handleRejectKYB(submission.id)}
                                >
                                  <FaTimes /> Reject
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <nav className="mt-4">
                <ul className="pagination justify-content-center">
                  <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </button>
                  </li>
                  {[...Array(totalPages)].map((_, index) => (
                    <li
                      key={index}
                      className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}
                    >
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(index + 1)}
                      >
                        {index + 1}
                      </button>
                    </li>
                  ))}
                  <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            )}
          </div>
        </div>

        {/* KYB Process Information */}
        <div className="card shadow mb-4">
          <div className="card-header py-3">
            <h6 className="m-0 font-weight-bold text-primary">KYB Verification Process</h6>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-3 mb-4">
                <div className="card h-100">
                  <div className="card-body">
                    <h5 className="card-title">
                      <FaBuilding className="me-2 text-primary" />
                      1. Business Registration
                    </h5>
                    <p className="card-text">
                      Verify that the business is legally registered with the appropriate authorities.
                    </p>
                    <ul className="list-group list-group-flush">
                      <li className="list-group-item">Check registration certificate</li>
                      <li className="list-group-item">Verify business name</li>
                      <li className="list-group-item">Confirm registration number</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-md-3 mb-4">
                <div className="card h-100">
                  <div className="card-body">
                    <h5 className="card-title">
                      <FaFileAlt className="me-2 text-success" />
                      2. Tax Compliance
                    </h5>
                    <p className="card-text">
                      Ensure that the business has valid tax identification and is compliant with tax regulations.
                    </p>
                    <ul className="list-group list-group-flush">
                      <li className="list-group-item">Verify tax ID number</li>
                      <li className="list-group-item">Check tax clearance certificate</li>
                      <li className="list-group-item">Confirm tax registration status</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-md-3 mb-4">
                <div className="card h-100">
                  <div className="card-body">
                    <h5 className="card-title">
                      <FaIdCard className="me-2 text-warning" />
                      3. Owner Verification
                    </h5>
                    <p className="card-text">
                      Verify the identity of the business owner and their relationship to the business.
                    </p>
                    <ul className="list-group list-group-flush">
                      <li className="list-group-item">Check owner's ID documents</li>
                      <li className="list-group-item">Verify ownership documents</li>
                      <li className="list-group-item">Confirm owner's KYC status</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-md-3 mb-4">
                <div className="card h-100">
                  <div className="card-body">
                    <h5 className="card-title">
                      <FaCheck className="me-2 text-info" />
                      4. Final Decision
                    </h5>
                    <p className="card-text">
                      Make a decision based on all verification results and provide clear feedback.
                    </p>
                    <ul className="list-group list-group-flush">
                      <li className="list-group-item">Approve if all checks pass</li>
                      <li className="list-group-item">Reject with specific reason</li>
                      <li className="list-group-item">Document verification decision</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Main render
  return (
    <AdminLayout>
      {renderContent()}
    </AdminLayout>
  );
}
