'use client';

import { useState } from 'react';

export default function TestClientPage() {
  const [count, setCount] = useState(0);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-4">Test Client Page</h1>
      <p className="mb-4">This is a test page to check if client-side code is working correctly.</p>
      <p className="mb-4">Count: {count}</p>
      <button
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        onClick={() => setCount(count + 1)}
      >
        Increment
      </button>
    </div>
  );
}
