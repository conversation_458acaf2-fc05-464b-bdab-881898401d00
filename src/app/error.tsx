'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faArrowLeft, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import MainLayout from '@/layouts/MainLayout';
import '@/styles/error-boundary.css';

/**
 * Global error component for handling unexpected runtime errors
 * This component is automatically used by Next.js when an error occurs in a route
 */
export default function Error({
  error,
  reset
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // Log the error to console in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.error('Application error:', error);
    }
  }, [error]);

  return (
    <MainLayout>
      <div className="error-page py-5">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8 text-center">
              <div className="error-image mb-4">
                <FontAwesomeIcon
                  icon={faExclamationTriangle}
                  className="text-warning error-icon-large"
                />
              </div>

              <h1 className="display-4 fw-bold mb-3">Something Went Wrong</h1>

              <p className="lead text-muted mb-4">
                We're sorry, but something went wrong on our end. This is not your fault.
                Our team has been notified and we're working to fix the issue.
              </p>

              <div className="d-flex flex-column flex-md-row justify-content-center gap-3 mb-5">
                <button
                  type="button"
                  onClick={() => reset()}
                  className="btn btn-primary btn-lg"
                >
                  <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
                  Try Again
                </button>

                <Link href="/" className="btn btn-outline-primary btn-lg">
                  <FontAwesomeIcon icon={faHome} className="me-2" />
                  Back to Home
                </Link>
              </div>

              <div className="error-details p-4 bg-light rounded-3 text-start">
                <h5 className="mb-3">Error Details</h5>
                <p className="mb-0 text-danger">
                  {error?.message || 'An unexpected error occurred'}
                </p>
                {process.env.NODE_ENV === 'development' && error?.stack && (
                  <pre className="mt-3 p-3 bg-dark text-light rounded small error-stack-trace">
                    {error.stack}
                  </pre>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
