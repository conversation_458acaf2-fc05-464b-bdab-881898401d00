'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import ModernSuperAdminDashboardLayout from '@/layouts/ModernSuperAdminDashboardLayout';
import EnhancedDashboardBanner from '@/components/dashboard/EnhancedDashboardBanner';
import ModernStatCard from '@/components/dashboard/ModernStatCard';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers, faBuilding, faCheckCircle, faListAlt } from '@fortawesome/free-solid-svg-icons';

export default function SuperAdminTestDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect if user is not a super admin
    if (!loading && user) {
      if (!user.role || !user.role.includes('super_admin')) {
        router.push('/login');
      }
    }
  }, [user, loading, router]);

  if (loading || !user) {
    return (
      <ModernSuperAdminDashboardLayout>
        <div className="d-flex justify-content-center align-items-center" style={{ height: '70vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </ModernSuperAdminDashboardLayout>
    );
  }

  return (
    <ModernSuperAdminDashboardLayout>
      <div className="modern-main-content super-admin-dashboard super-admin-dashboard-page">
        {/* Enhanced Dashboard Banner */}
        <EnhancedDashboardBanner
          title="Super Admin Dashboard"
          subtitle="Welcome back, Super Admin"
          className="super-admin-banner"
          user={{
            name: user?.name || 'Super Admin',
            email: user?.email || '<EMAIL>',
            profilePictureUrl: user?.profileImage || '/assets/img/default-avatar.png',
            role: 'Super Admin',
            joinDate: 'Joined Jan 2023',
          }}
        />

        {/* Stats Grid */}
        <div className="modern-section-header">
          <h2 className="modern-section-title">Dashboard Overview</h2>
          <p className="modern-section-subtitle">Key metrics at a glance</p>
        </div>

        <div className="modern-stats-grid">
          <ModernStatCard
            title="Total Users"
            value="1,234"
            icon={<FontAwesomeIcon icon={faUsers} />}
            change="+12%"
            changeType="positive"
            period="vs last month"
          />
          <ModernStatCard
            title="Total Businesses"
            value="567"
            icon={<FontAwesomeIcon icon={faBuilding} />}
            change="+8%"
            changeType="positive"
            period="vs last month"
          />
          <ModernStatCard
            title="Pending Verifications"
            value="45"
            icon={<FontAwesomeIcon icon={faCheckCircle} />}
            change="-5%"
            changeType="negative"
            period="vs last month"
          />
          <ModernStatCard
            title="Active Listings"
            value="890"
            icon={<FontAwesomeIcon icon={faListAlt} />}
            change="+15%"
            changeType="positive"
            period="vs last month"
          />
        </div>

        {/* Content Grid */}
        <div className="modern-content-grid">
          {/* Left Column */}
          <div>
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Recent Activity</h3>
              </div>
              <div className="modern-card-body">
                <p>This is where recent activity would be displayed.</p>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div>
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Quick Actions</h3>
              </div>
              <div className="modern-card-body">
                <p>This is where quick actions would be displayed.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModernSuperAdminDashboardLayout>
  );
}
