'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import ModernSuperAdminDashboardLayout from '@/layouts/ModernSuperAdminDashboardLayout';

export default function SuperAdminTestDirect() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect if user is not a super admin
    if (!loading && user) {
      if (!user.role || !user.role.includes('super_admin')) {
        router.push('/login');
      }
    }
  }, [user, loading, router]);

  if (loading || !user) {
    return (
      <ModernSuperAdminDashboardLayout>
        <div className="d-flex justify-content-center align-items-center" style={{ height: '70vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </ModernSuperAdminDashboardLayout>
    );
  }

  return (
    <ModernSuperAdminDashboardLayout>
      <div className="modern-main-content super-admin-dashboard" role="main" aria-label="Dashboard content">
        <div style={{ 
          width: '100%', 
          padding: '20px', 
          backgroundColor: '#f0f0f0', 
          marginTop: '20px', 
          textAlign: 'center',
          border: '2px solid red'
        }}>
          <h1>Super Admin Test Page (Direct)</h1>
          <p>This is a test page to verify that the Super Admin Dashboard layout is working correctly.</p>
          <div style={{ 
            backgroundColor: 'white', 
            padding: '20px', 
            borderRadius: '8px', 
            marginTop: '20px',
            border: '2px solid blue'
          }}>
            <h2>Content Test</h2>
            <p>If you can see this content, the Super Admin Dashboard layout is working correctly.</p>
          </div>
        </div>
      </div>
    </ModernSuperAdminDashboardLayout>
  );
}
