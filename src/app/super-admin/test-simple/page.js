'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import SimpleSuperAdminLayout from '@/layouts/SimpleSuperAdminLayout';

export default function SuperAdminTestSimple() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect if user is not a super admin
    if (!loading && user) {
      if (!user.role || !user.role.includes('super_admin')) {
        router.push('/login');
      }
    }
  }, [user, loading, router]);

  if (loading || !user) {
    return (
      <SimpleSuperAdminLayout>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
          <div style={{ border: '4px solid #f3f3f3', borderTop: '4px solid #359e04', borderRadius: '50%', width: '40px', height: '40px', animation: 'spin 1s linear infinite' }}>
            <span style={{ position: 'absolute', width: '1px', height: '1px', padding: '0', margin: '-1px', overflow: 'hidden', clip: 'rect(0, 0, 0, 0)', whiteSpace: 'nowrap', borderWidth: '0' }}>Loading...</span>
          </div>
        </div>
      </SimpleSuperAdminLayout>
    );
  }

  return (
    <SimpleSuperAdminLayout>
      <div style={{ 
        width: '90%', 
        maxWidth: '1800px', 
        margin: '0 auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch'
      }}>
        <div style={{ 
          width: '100%', 
          padding: '20px', 
          backgroundColor: '#f0f0f0', 
          marginTop: '20px', 
          textAlign: 'center',
          border: '2px solid red',
          borderRadius: '8px'
        }}>
          <h1 style={{ color: '#333' }}>Super Admin Test Page (Simple Layout)</h1>
          <p>This is a test page to verify that the Super Admin Dashboard layout is working correctly.</p>
          <div style={{ 
            backgroundColor: 'white', 
            padding: '20px', 
            borderRadius: '8px', 
            marginTop: '20px',
            border: '2px solid blue'
          }}>
            <h2 style={{ color: '#359e04' }}>Content Test</h2>
            <p>If you can see this content, the Super Admin Dashboard layout is working correctly.</p>
          </div>
        </div>
        
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(4, 1fr)', 
          gap: '20px', 
          marginTop: '20px' 
        }}>
          {[1, 2, 3, 4].map(item => (
            <div key={item} style={{ 
              backgroundColor: 'white', 
              padding: '20px', 
              borderRadius: '8px', 
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
              border: '1px solid #e5e7eb'
            }}>
              <h3 style={{ color: '#359e04' }}>Card {item}</h3>
              <p>This is a test card to verify that grid layouts work correctly.</p>
            </div>
          ))}
        </div>
      </div>
    </SimpleSuperAdminLayout>
  );
}
