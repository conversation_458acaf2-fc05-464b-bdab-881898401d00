'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

export default function ContentRedirect() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    // Redirect if user is not a super admin
    if (!loading) {
      if (!user || user.role !== 'super_admin') {
        router.push('/login');
      } else {
        // Redirect to the pages section of content management
        router.push('/super-admin/content/pages');
      }
    }
  }, [user, loading, router]);

  return (
    <div className="container py-5 text-center">
      <div className="spinner-border text-primary" role="status">
        <span className="visually-hidden">Loading...</span>
      </div>
      <p className="mt-3">Redirecting to content management...</p>
    </div>
  );
}
