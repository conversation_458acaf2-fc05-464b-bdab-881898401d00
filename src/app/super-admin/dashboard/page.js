'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import ModernSuperAdminDashboardLayout from '@/layouts/ModernSuperAdminDashboardLayout';
import EnhancedStatCard from '@/components/dashboard/EnhancedStatCard';
import ModernChartCard from '@/components/dashboard/ModernChartCard';
import ModernActivityCard from '@/components/dashboard/ModernActivityCard';
import EnhancedDashboardBanner from '@/components/dashboard/EnhancedDashboardBanner';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faStore,
  faUserShield,
  faChartLine,
  faClipboardList,
  faUserCog,
  faCheckCircle,
  faChevronRight,
  faCog,
  faFileAlt,
  faSitemap,
  faWallet,
  faGlobe
} from '@fortawesome/free-solid-svg-icons';

// Import necessary styles
import '@/styles/dashboard-page.css';
import '@/styles/dashboard-banner.css';
import '@/styles/dashboard-content.css';
import '@/styles/dashboard-banner-spacing.css';
import '@/styles/super-admin-user-dashboard-consistency.css';
import '@/styles/fix-notification-card-width.css';
import '@/styles/fix-breadcrumb-width.css';

export default function SuperAdminDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBusinesses: 0,
    totalAdmins: 0,
    pendingVerifications: 0,
    activeListings: 0,
    recentSignups: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [notification, setNotification] = useState("Welcome to the Super Admin Dashboard");
  const [recentActivity, setRecentActivity] = useState([]);
  const [networkTab, setNetworkTab] = useState('users');

  useEffect(() => {
    // Redirect if user is not a super admin
    if (!loading && user) {
      if (!user.role || user.role !== 'super_admin') {
        router.push('/login');
      } else {
        fetchDashboardStats();
      }
    }
  }, [user, loading, router]);

  const fetchDashboardStats = async () => {
    try {
      setIsLoading(true);
      // In a real implementation, this would be an API call
      // For now, we'll use mock data
      setTimeout(() => {
        setStats({
          totalUsers: 156,
          totalBusinesses: 42,
          totalAdmins: 5,
          pendingVerifications: 12,
          activeListings: 89,
          recentSignups: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'customer', date: '2023-06-15' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'business_owner', date: '2023-06-14' },
            { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'customer', date: '2023-06-13' }
          ]
        });

        // Mock recent activity
        setRecentActivity([
          {
            id: 'act-1',
            type: 'user',
            title: 'New user registered: John Doe',
            time: '2023-04-02T14:30:00Z',
            icon: faUsers,
            iconColor: 'primary',
            link: '/super-admin/users'
          },
          {
            id: 'act-2',
            type: 'business',
            title: 'New business listing: Tech Repair Shop',
            time: '2023-03-25T10:15:00Z',
            icon: faStore,
            iconColor: 'success',
            link: '/super-admin/businesses'
          },
          {
            id: 'act-3',
            type: 'verification',
            title: 'New KYB verification request from City Dental Clinic',
            time: '2023-03-20T09:45:00Z',
            icon: faUserCog,
            iconColor: 'warning',
            link: '/super-admin/verifications'
          },
          {
            id: 'act-4',
            type: 'admin',
            title: 'New admin user created: Sarah Johnson',
            time: '2023-03-18T16:20:00Z',
            icon: faUserShield,
            iconColor: 'danger',
            link: '/super-admin/admins'
          }
        ]);

        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      setIsLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format time (relative)
  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return formatDate(dateString);
    }
  };

  // Function to dismiss a notification
  const dismissNotification = () => {
    setNotification(null);
  };

  if (loading || !user) {
    return (
      <ModernSuperAdminDashboardLayout>
        <div className="d-flex justify-content-center align-items-center" style={{ height: '70vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </ModernSuperAdminDashboardLayout>
    );
  }

  return (
    <ModernSuperAdminDashboardLayout>
      <div className="modern-main-content super-admin-dashboard super-admin-dashboard-page" role="main" aria-label="Dashboard content">
        <EnhancedDashboardBanner
          user={user || {
            name: 'Super Admin',
            email: '<EMAIL>',
            profileImage: '/assets/img/default-avatar.png'
          }}
          stats={{
            totalUsers: stats.totalUsers,
            totalBusinesses: stats.totalBusinesses,
            pendingVerifications: stats.pendingVerifications
          }}
          backgroundImageUrl="/assets/img/dashboard-banner-bg.jpg"
          userType="super_admin"
          onQuickActionClick={(action) => console.log('Quick action clicked:', action)}
        />

        {notification && (
          <div className="modern-notification success mb-4" role="alert">
            <div className="notification-content">
              <div className="notification-icon">
                <FontAwesomeIcon icon={faCheckCircle} />
              </div>
              <div className="notification-text">
                <div className="notification-title">Welcome!</div>
                <div className="notification-message">{notification}</div>
              </div>
            </div>
            <button
              type="button"
              className="notification-close"
              aria-label="Close"
              onClick={dismissNotification}
            >
              <FontAwesomeIcon icon={faChevronRight} />
            </button>
          </div>
        )}

        <nav aria-label="breadcrumb" className="modern-breadcrumb mb-4">
          <ol className="breadcrumb">
            <li className="breadcrumb-item"><Link href="/">Home</Link></li>
            <li className="breadcrumb-item active" aria-current="page">Super Admin Dashboard</li>
          </ol>
        </nav>

        <div className="container-fluid">
          {/* Stats Row */}
          <div className="modern-stats-grid">
            <EnhancedStatCard
              title="Total Users"
              value={stats.totalUsers}
              label="Registered users"
              icon={faUsers}
              iconColor="primary"
              href="/super-admin/users"
              trend={{ value: 15, direction: 'up' }}
            />
            <EnhancedStatCard
              title="Total Businesses"
              value={stats.totalBusinesses}
              label="Registered businesses"
              icon={faStore}
              iconColor="success"
              href="/super-admin/businesses"
              trend={{ value: 8, direction: 'up' }}
            />
            <EnhancedStatCard
              title="Admin Users"
              value={stats.totalAdmins}
              label="Platform administrators"
              icon={faUserShield}
              iconColor="danger"
              href="/super-admin/admins"
              trend={{ value: 2, direction: 'up' }}
            />
            <EnhancedStatCard
              title="Pending Verifications"
              value={stats.pendingVerifications}
              label="Awaiting review"
              icon={faUserCog}
              iconColor="warning"
              href="/super-admin/verifications"
            />
          </div>

          {/* Main Content Grid */}
          <div className="modern-content-grid">
            {/* Chart and Activity Section */}
            <div className="main-content-area">
              <ModernChartCard
                title="Platform Overview"
                actions={[
                  { label: 'Monthly View', onClick: () => console.log('Monthly view') },
                  { label: 'Weekly View', onClick: () => console.log('Weekly view') },
                  { label: 'Yearly View', onClick: () => console.log('Yearly view') },
                ]}
              >
                <div
                  id="dashboard-chart"
                  className="chart-container"
                  style={{ height: '300px' }}
                  role="img"
                  aria-label="Platform activity chart showing user engagement over time"
                >
                  <div className="modern-chart-placeholder">
                    <div className="chart-placeholder-icon">
                      <FontAwesomeIcon icon={faChartLine} aria-hidden="true" />
                    </div>
                    <div className="chart-placeholder-content">
                      <h4 className="chart-placeholder-title">Platform Overview</h4>
                      <p className="chart-placeholder-text">Platform activity data will be displayed here</p>
                    </div>
                    <div className="chart-placeholder-data">
                      <div className="chart-data-point" style={{ height: '30%' }}></div>
                      <div className="chart-data-point" style={{ height: '50%' }}></div>
                      <div className="chart-data-point" style={{ height: '70%' }}></div>
                      <div className="chart-data-point" style={{ height: '60%' }}></div>
                      <div className="chart-data-point" style={{ height: '80%' }}></div>
                      <div className="chart-data-point" style={{ height: '40%' }}></div>
                      <div className="chart-data-point" style={{ height: '60%' }}></div>
                    </div>
                  </div>
                </div>
              </ModernChartCard>

              <div className="activity-grid">
                <ModernActivityCard
                  title="Recent Activity"
                  items={recentActivity}
                  renderItem={(activity) => (
                    <div className="modern-activity-item-content">
                      <div className="modern-activity-icon" style={{ backgroundColor: `var(--${activity.iconColor}-light)`, color: `var(--${activity.iconColor})` }}>
                        <FontAwesomeIcon icon={activity.icon} />
                      </div>
                      <div className="modern-activity-content">
                        <div className="modern-activity-title">
                          <Link href={activity.link}>{activity.title}</Link>
                        </div>
                        <div className="modern-activity-time">
                          {formatRelativeTime(activity.time)}
                        </div>
                      </div>
                    </div>
                  )}
                  viewAllLink="/super-admin/activity"
                  emptyMessage="No recent activity to display"
                />

                <ModernActivityCard
                  title="Recent Signups"
                  items={stats.recentSignups}
                  renderItem={(signup) => (
                    <div className="modern-activity-item-content">
                      <div className="modern-activity-icon" style={{ backgroundColor: 'var(--primary-50)', color: 'var(--primary)' }}>
                        <FontAwesomeIcon icon={faUsers} />
                      </div>
                      <div className="modern-activity-content">
                        <div className="modern-activity-title">
                          <Link href={`/super-admin/users/${signup.id}`}>{signup.name}</Link>
                        </div>
                        <div className="modern-activity-meta">
                          <span className="modern-activity-email">{signup.email}</span>
                          <span className="modern-activity-role">{signup.role.replace('_', ' ')}</span>
                        </div>
                        <div className="modern-activity-time">
                          {formatRelativeTime(signup.date)}
                        </div>
                      </div>
                    </div>
                  )}
                  viewAllLink="/super-admin/users"
                  emptyMessage="No recent signups to display"
                />
              </div>
            </div>

            {/* Sidebar Content */}
            <div className="sidebar-content">
              <div className="modern-card mb-4">
                <div className="modern-card-header">
                  <h5 className="modern-card-title" id="users-listings-tabs">Platform Data</h5>
                  <div className="modern-tabs">
                    <button
                      className={`modern-tab ${networkTab === 'users' ? 'active' : ''}`}
                      onClick={() => setNetworkTab('users')}
                      type="button"
                      role="tab"
                      aria-controls="users-content"
                      aria-selected={networkTab === 'users'}
                    >
                      Users
                    </button>
                    <button
                      className={`modern-tab ${networkTab === 'listings' ? 'active' : ''}`}
                      onClick={() => setNetworkTab('listings')}
                      type="button"
                      role="tab"
                      aria-controls="listings-content"
                      aria-selected={networkTab === 'listings'}
                    >
                      Listings
                    </button>
                  </div>
                </div>
                <div className="modern-card-body p-0">
                  <div className="tab-content">
                    <div
                      className={`tab-pane fade ${networkTab === 'users' ? 'show active' : ''}`}
                      role="tabpanel"
                      aria-labelledby="users-tab"
                    >
                      <ul className="modern-user-list">
                        {stats.recentSignups.map((user) => (
                          <li key={user.id} className="modern-user-item fade-in delay-1">
                            <div className="modern-user-avatar">
                              <img
                                src={`/assets/img/avatar-${user.id}.jpg`}
                                alt="User avatar"
                                width="48"
                                height="48"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.src = '/assets/img/default-avatar.png';
                                }}
                              />
                            </div>
                            <div className="modern-user-info">
                              <h6 className="modern-user-name">{user.name}</h6>
                              <p className="modern-user-location">{user.role.replace('_', ' ')}</p>
                            </div>
                            <Link href={`/super-admin/users/${user.id}`} className="modern-view-button">View</Link>
                          </li>
                        ))}
                        <li className="modern-list-footer">
                          <Link href="/super-admin/users" className="modern-view-all">
                            View All Users
                            <FontAwesomeIcon icon={faChevronRight} className="ms-1" size="xs" />
                          </Link>
                        </li>
                      </ul>
                    </div>
                    <div
                      className={`tab-pane fade ${networkTab === 'listings' ? 'show active' : ''}`}
                      role="tabpanel"
                      aria-labelledby="listings-tab"
                    >
                      <ul className="modern-invoice-list">
                        <li className="modern-invoice-item fade-in delay-1">
                          <div className="modern-invoice-info">
                            <h6 className="modern-invoice-title">Luxury Spa & Wellness</h6>
                            <p className="modern-invoice-id">Health & Beauty</p>
                          </div>
                          <span className="modern-invoice-status success">Active</span>
                        </li>
                        <li className="modern-invoice-item fade-in delay-2">
                          <div className="modern-invoice-info">
                            <h6 className="modern-invoice-title">City Dental Clinic</h6>
                            <p className="modern-invoice-id">Healthcare</p>
                          </div>
                          <span className="modern-invoice-status warning">Pending</span>
                        </li>
                        <li className="modern-invoice-item fade-in delay-3">
                          <div className="modern-invoice-info">
                            <h6 className="modern-invoice-title">Tech Repair Shop</h6>
                            <p className="modern-invoice-id">Electronics</p>
                          </div>
                          <span className="modern-invoice-status danger">Suspended</span>
                        </li>
                        <li className="modern-invoice-item fade-in delay-4">
                          <div className="modern-invoice-info">
                            <h6 className="modern-invoice-title">Gourmet Restaurant</h6>
                            <p className="modern-invoice-id">Food & Drink</p>
                          </div>
                          <span className="modern-invoice-status secondary">Inactive</span>
                        </li>
                        <li className="modern-list-footer">
                          <Link href="/super-admin/listings" className="modern-view-all">
                            View All Listings
                            <FontAwesomeIcon icon={faChevronRight} className="ms-1" size="xs" />
                          </Link>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="modern-quick-links">
            <div className="modern-section-header">
              <h2 className="modern-section-title">Quick Access</h2>
              <p className="modern-section-subtitle">Shortcuts to frequently used features</p>
            </div>

            <div className="modern-quick-links-grid">
              <Link href="/super-admin/settings" className="modern-quick-link-card fade-in delay-1">
                <div className="quick-link-icon" style={{ backgroundColor: 'var(--primary-50)', color: 'var(--primary)' }}>
                  <FontAwesomeIcon icon={faCog} />
                </div>
                <div className="quick-link-content">
                  <h3 className="quick-link-title">Platform Settings</h3>
                  <p className="quick-link-description">Configure global platform settings and preferences</p>
                </div>
              </Link>

              <Link href="/super-admin/content/pages" className="modern-quick-link-card fade-in delay-2">
                <div className="quick-link-icon" style={{ backgroundColor: 'var(--success-light)', color: 'var(--success)' }}>
                  <FontAwesomeIcon icon={faFileAlt} />
                </div>
                <div className="quick-link-content">
                  <h3 className="quick-link-title">Content Management</h3>
                  <p className="quick-link-description">Manage static pages and platform content</p>
                </div>
              </Link>

              <Link href="/super-admin/navigation" className="modern-quick-link-card fade-in delay-3">
                <div className="quick-link-icon" style={{ backgroundColor: 'var(--warning-light)', color: 'var(--warning)' }}>
                  <FontAwesomeIcon icon={faSitemap} />
                </div>
                <div className="quick-link-content">
                  <h3 className="quick-link-title">Navigation Management</h3>
                  <p className="quick-link-description">Configure site navigation and menu structure</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </ModernSuperAdminDashboardLayout>
  );
}
