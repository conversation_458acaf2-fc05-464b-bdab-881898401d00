'use client';

import { initCryptoWalletHandler } from './cryptoWalletHandler';

/**
 * Initialize the application
 * This function should be called early in the application lifecycle
 */
export function initializeApp() {
  if (typeof window === 'undefined') return; // Skip on server-side

  // Initialize crypto wallet handler to prevent errors (disabled for now)
  // initCryptoWalletHandler();

  // Add global error handler for unhandled errors
  window.addEventListener('error', (event) => {
    // Handle specific errors
    if (event.message && event.message.includes('API request failed with status 404')) {
      // Prevent 404 API errors from showing in the console in development
      if (process.env.NODE_ENV === 'development') {
        event.preventDefault();
        console.debug('Suppressed 404 API error in development mode');
      }
    }
  });

  // Add unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    // Handle specific promise rejections
    if (
      event.reason &&
      typeof event.reason.message === 'string' &&
      event.reason.message.includes('API request failed with status 404')
    ) {
      // Prevent 404 API errors from showing in the console in development
      if (process.env.NODE_ENV === 'development') {
        event.preventDefault();
        console.debug('Suppressed unhandled 404 API promise rejection in development mode');
      }
    }
  });
}

export default { initializeApp };
