'use client';

/**
 * Utility to handle crypto wallet provider conflicts
 * This helps prevent errors from crypto wallet browser extensions
 * that try to inject themselves into the page
 */

/**
 * Initialize the crypto wallet handler
 * This should be called early in the application lifecycle
 */
export function initCryptoWalletHandler() {
  if (typeof window === 'undefined') return; // Skip on server-side

  try {
    // Store the original ethereum provider if it exists
    const originalEthereum = window.ethereum;

    // Prevent extensions from redefining ethereum property
    // by creating a non-configurable property
    if (!window.hasOwnProperty('ethereum')) {
      Object.defineProperty(window, 'ethereum', {
        value: originalEthereum,
        writable: true,
        enumerable: true,
        configurable: false // This prevents redefining the property
      });
    }

    // Handle errors from wallet extensions trying to redefine ethereum
    window.addEventListener('error', (event) => {
      if (
        (event.message && event.message.includes('Cannot redefine property: ethereum')) ||
        (event.error && event.error.message && event.error.message.includes('Cannot redefine property: ethereum'))
      ) {
        // Prevent the error from showing in the console
        event.preventDefault();
        console.debug('Prevented crypto wallet extension conflict');
      }
    }, true); // Use capture phase to catch the error before it reaches the console

  } catch (error) {
    console.debug('Error initializing crypto wallet handler:', error);
  }
}

export default { initCryptoWalletHandler };
