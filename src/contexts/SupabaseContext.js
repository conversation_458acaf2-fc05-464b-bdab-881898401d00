'use client';

import React, { createContext, useContext } from 'react';
import axios from 'axios';

// Create a context for MongoDB API access
const SupabaseContext = createContext(null);

// MongoDB API client that mimics Supabase's API
const mongodbClient = {
  // RPC function to increment popular search
  rpc: async (functionName, params) => {
    if (functionName === 'increment_popular_search') {
      try {
        await axios.post('/api/search/popular', {
          query: params.search_query,
          location: params.search_location,
          category: params.search_category
        });
        return { data: true, error: null };
      } catch (error) {
        console.error(`Error calling ${functionName}:`, error);
        return { data: null, error: error.message };
      }
    }
    return { data: null, error: 'Function not implemented' };
  },
  
  // From function to query a collection
  from: (tableName) => ({
    select: (fields) => ({
      order: (orderBy) => ({
        eq: (field, value) => ({
          single: async () => {
            try {
              const response = await axios.get(`/api/${tableName}/${value}`);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error: error.message };
            }
          },
          get: async () => {
            try {
              const response = await axios.get(`/api/${tableName}?${field}=${value}`);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error: error.message };
            }
          }
        }),
        get: async () => {
          try {
            const response = await axios.get(`/api/${tableName}?orderBy=${orderBy}`);
            return { data: response.data, error: null };
          } catch (error) {
            return { data: null, error: error.message };
          }
        }
      }),
      eq: (field, value) => ({
        get: async () => {
          try {
            const response = await axios.get(`/api/${tableName}?${field}=${value}`);
            return { data: response.data, error: null };
          } catch (error) {
            return { data: null, error: error.message };
          }
        }
      }),
      get: async () => {
        try {
          const response = await axios.get(`/api/${tableName}`);
          return { data: response.data, error: null };
        } catch (error) {
          return { data: null, error: error.message };
        }
      }
    }),
    insert: async (data) => {
      try {
        const response = await axios.post(`/api/${tableName}`, data);
        return { data: response.data, error: null };
      } catch (error) {
        return { data: null, error: error.message };
      }
    },
    update: (data) => ({
      eq: (field, value) => ({
        single: async () => {
          try {
            const response = await axios.put(`/api/${tableName}/${value}`, data);
            return { data: response.data, error: null };
          } catch (error) {
            return { data: null, error: error.message };
          }
        }
      })
    })
  })
};

// Provider component
export function SupabaseProvider({ children }) {
  return (
    <SupabaseContext.Provider value={{ supabase: mongodbClient }}>
      {children}
    </SupabaseContext.Provider>
  );
}

// Hook to use the Supabase context
export function useSupabase() {
  const context = useContext(SupabaseContext);
  if (!context) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
}

export default SupabaseContext;
