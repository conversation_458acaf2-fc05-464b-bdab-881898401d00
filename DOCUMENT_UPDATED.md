# Finda: Local Business Directory Platform - Developer Documentation

## Table of Contents
- [1. Introduction](#1-introduction)
- [2. Technology Stack](#2-technology-stack)
- [3. Application Architecture](#3-application-architecture)
- [4. Database Schema](#4-database-schema)
- [5. User Roles and Permissions](#5-user-roles-and-permissions)
- [6. User Flows](#6-user-flows)
- [7. Feature Implementation Details](#7-feature-implementation-details)
- [8. UI Standards and Design Guidelines](#8-ui-standards-and-design-guidelines)
- [9. Security Considerations](#9-security-considerations)
- [10. Testing Strategy](#10-testing-strategy)
- [11. Deployment](#11-deployment)
- [12. Appendix](#12-appendix)

## 1. Introduction

Finda is a web platform designed to connect local businesses with customers. It combines features from business directories like Yelp and Google Business with integrated booking capabilities, a blog for content marketing, and a newsletter for user engagement. The platform also incorporates robust KYC (Know Your Customer) and KYB (Know Your Business) verification processes to ensure trust and security, along with a verification badge system to visually indicate verified users and businesses. This document outlines the application's architecture, user flows, features, technical specifications, monetization strategies, and development guidelines. It is a *living document* and should be updated regularly as the application evolves.

## 2. Technology Stack

### Frontend & Backend
* **Next.js (React, Node.js)**: A React framework for building server-rendered and statically generated web applications. Provides features like routing, API routes, and optimized performance.
  * **Version**: [Specify current version]
  * **Architecture**: Pages Router (not App Router)

### Styling
* **Tailwind CSS**: Utility-first CSS framework for styling and layout
* **Custom styles**: Project-specific design in `styles.css`

### Database
* **MongoDB**: NoSQL document database used as the primary database service
* **Mongoose**: Object Data Mapper (ODM) for MongoDB that provides schema-based solution

### Authentication
* **Custom JWT-based authentication**: Using MongoDB for storage and Next.js API routes for handling authentication flows
* **NextAuth.js**: Handles authentication complexities with JWT support

### File Uploads
* **Dropzone.js**: Client-side file upload handling
* **Multer**: Server-side middleware for handling file uploads
* **Cloudinary**: Cloud service for image hosting, transformation, and optimization

### Mapping
* **Google Maps JavaScript API**: For displaying maps and business locations

### Rich Text Editing (Blog)
* **TipTap**: Rich text editor for creating and formatting blog content

### Payment Gateways
* **Paystack and Flutterwave**: Handles online payments and subscription billing

### Email Service
* **Mailgun**: For sending newsletters, transactional emails, and verification emails

### Charting (Dashboard)
* **Morris.js**: For business owner analytics

### Search
* **[Specify which search technology is implemented]**: For search functionality across the platform

### Other Libraries/Tools
* **slugify**: For creating URL-friendly slugs
* **isomorphic-dompurify**: For sanitizing user-generated HTML content
* **axios**: Promise-based HTTP client for browser and Node.js
* **Font Awesome 6**: Icon library used throughout the application

### Performance Optimization
* **Lazy loading**: For images and components to improve initial page load time
* **Code splitting**: Via Next.js to split JavaScript bundle into smaller chunks
* **Caching**: Strategies to improve performance and reduce server load
* **Pagination**: For data-heavy pages to improve loading times
* **Real-time features**: For notifications, messages, and other dynamic content

### Deployment
* **Frontend & Backend**: Vercel (recommended for Next.js applications)

## 3. Application Architecture

Finda follows a client-server architecture, implemented using Next.js:

### Client (Frontend - Next.js/React)
The frontend is responsible for the user interface, handling user interactions, and displaying data. It's built as a React application using Next.js. It communicates with the backend via API calls (using `fetch` or `axios`).

### Server (Backend - Next.js API Routes)
Next.js provides API routes, which are serverless functions that handle backend logic. These API routes:
* Expose a RESTful API
* Handle requests from the frontend
* Perform data validation
* Interact with the MongoDB database (using Mongoose)
* Integrate with external services (Cloudinary, Paystack/Flutterwave, Mailgun)
* Generate and verify JWTs

### Data Flow Diagram
[Description: This section would include a diagram showing the flow of data between client, server, database, and external services]

## 4. Database Schema

The MongoDB database schema is defined using Mongoose schemas. These schemas define the structure and data types of the documents stored in the database.

### Model Relationships
* **User → Business**: One-to-many relationship. A user (business owner) can own multiple businesses.
* **User → Review**: One-to-many relationship. A user can write multiple reviews.
* **Business → Review**: One-to-many relationship. A business can have multiple reviews.
* **User → Bookmark**: One-to-many relationship. A user can bookmark multiple businesses.
* **Business → Bookmark**: One-to-many relationship. A business can be bookmarked by multiple users.
* **User → Message**: Two one-to-many relationships. A user can send multiple messages and receive multiple messages.
* **Business → Booking**: One-to-many relationship. A business can have multiple bookings.
* **User → Booking**: One-to-many relationship. A user can make multiple bookings.
* **User → BlogPost**: One-to-many relationship. A user (content manager) can author multiple blog posts.

### Indexing Strategy
* **Single Field Indexes**: Used for frequently queried fields like `email` in the User schema
* **Compound Indexes**: Used for queries that filter on multiple fields, like `businessId` and `userId` in the Review schema
* **Text Indexes**: Used for text search functionality on fields like `name` and `description` in the Business schema
* **Sparse Indexes**: Used for fields that may not exist in all documents, like `stripe_customer_id` in the User schema

[Note: The detailed schema definitions would follow here but are omitted for brevity]

## 5. User Roles and Permissions

The application has five primary user roles, each with specific permissions and capabilities:

### Permissions Matrix

| Feature/Action | Customer | Business Owner | Admin | Super Admin | Content Manager |
|----------------|----------|----------------|-------|-------------|-----------------|
| View business listings | ✅ | ✅ | ✅ | ✅ | ✅ |
| Search businesses | ✅ | ✅ | ✅ | ✅ | ✅ |
| Leave reviews | ✅ | ❌ | ✅ | ✅ | ✅ |
| Bookmark businesses | ✅ | ✅ | ✅ | ✅ | ✅ |
| Book appointments | ✅ | ✅ | ✅ | ✅ | ✅ |
| Message businesses | ✅ | ✅ | ✅ | ✅ | ✅ |
| Create business listings | ❌ | ✅ | ✅ | ✅ | ❌ |
| Edit own business | ❌ | ✅ | ✅ | ✅ | ❌ |
| Delete own business | ❌ | ✅ | ✅ | ✅ | ❌ |
| Manage all businesses | ❌ | ❌ | ✅ | ✅ | ❌ |
| Manage all users | ❌ | ❌ | ✅ | ✅ | ❌ |
| Moderate reviews | ❌ | ❌ | ✅ | ✅ | ❌ |
| Manage admins | ❌ | ❌ | ❌ | ✅ | ❌ |
| Manage system settings | ❌ | ❌ | ❌ | ✅ | ❌ |
| Create/edit blog posts | ❌ | ❌ | ✅ | ✅ | ✅ |
| Publish/unpublish blog posts | ❌ | ❌ | ✅ | ✅ | ✅ |
| Submit KYC documents | ✅ | ✅ | ✅ | ✅ | ✅ |
| Submit KYB documents | ❌ | ✅ | ✅ | ✅ | ❌ |
| Review KYC/KYB submissions | ❌ | ❌ | ✅ | ✅ | ❌ |
| Manage newsletter | ❌ | ❌ | ✅ | ✅ | ❌ |
| Apply to become business owner | ✅ | N/A | N/A | N/A | N/A |

### Role Descriptions

#### Customer
Can search for businesses, view business details, leave reviews and ratings, bookmark businesses, book appointments/reservations, and message businesses. They can also view the About, FAQ, and Contact pages, and the blog. They can subscribe and unsubscribe from the newsletter. Customers can also submit KYC information for verification and receive a verification badge upon approval. Customers can apply from their dashboard to become business owners.

#### Business Owner
Can create and manage their business profile(s), respond to reviews, enable/disable booking, manage bookings, and access marketing/analytics tools. They can also upgrade to premium listings, manage their subscription (if applicable), and create advertising campaigns (if applicable). Business owners can also submit KYB information for verification and receive a verification badge upon approval.

#### Admin
Can manage businesses, users, and reviews across the platform. Admins can approve or reject new business listings, moderate reviews, manage user accounts, and manage advertising campaigns and subscriptions. They can manage newsletter campaigns. Admins can also review and approve/reject KYC and KYB submissions.

#### Super Admin
Has unrestricted access to all platform features, including managing admins, modifying system settings, accessing advanced analytics, and managing monetization settings. They can manage newsletter campaigns and KYC/KYB verifications.

#### Content Manager
Can create, edit, publish, and delete blog posts. Can also manage blog categories and tags. They do not have access to other administrative functions (like managing users or businesses).

## 6. User Flows

[Note: This section would contain the detailed user flows for each role. For brevity, we're focusing on adding new documentation for key components.]

## 7. Feature Implementation Details

### 7.1. Header Component

The Header component is a critical UI element that appears on all pages with different behaviors based on the page type.

#### Header Types and Behavior

1. **Home Page Header**:
   * Has a green background (#359e04)
   * Uses both fixed and sticky headers
   * Fixed header remains at the top of the viewport
   * Sticky header appears when scrolling down

2. **Non-Home Page Header**:
   * Has a white background
   * Uses only the sticky header
   * Has padding that matches the sticky header
   * Same height as the home page header

#### Header Content

* **Logo**: Displayed on the left side
* **Navigation Links**: Displayed in the center
* **User Authentication**:
   * For non-logged-in users: Sign In and Sign Up buttons
   * For logged-in users: User avatar with dropdown menu

#### User Avatar Dropdown Menu

When a user is logged in, the header displays the user's avatar. Clicking on the avatar reveals a dropdown menu with:

* Role-specific navigation links
* Settings option
* Sign Out option
* Notification and message navigation links

#### Implementation Notes

* The header height should be consistent across all pages
* The main content should start underneath the header
* The header should remain visible at all times
* For non-home pages, there should be adequate padding at the top of the hero section to fill any gap between the banner and header

### 7.2. Dashboard Layouts

The application uses several dashboard layouts for different user roles, with consistent design principles across all dashboards.

#### Common Dashboard Design Elements

* **Primary Color**: #359e04 (green) used consistently across all dashboard sections
* **Width**: All sections use 90% width instead of 100% width
* **Top Margins**: 70px top margin for main content, 10px top margin for dashboard banner
* **Banner Design**: Rounded corners (curved edges) for visual consistency
* **Content Height**: Flexible height based on actual content rather than fixed constraints
* **Sidebar**: Enhanced sidebar that doesn't overlap with main content
* **Footer**: Extends across both sidebar and main content areas

#### User Dashboard Layout

* **Banner**: Displays username with user avatar positioned more to the left
* **Avatar Size**: Same size as the super admin dashboard banner avatar
* **Profile Completion Bar**: Shorter bar on the user dashboard banner
* **Sections**: Includes enhanced sections for:
  * Saved Places
  * My Bookings
  * My Profile
  * Messages (with green icons instead of purple)
  * Notifications
  * Wallet (includes Payment History)
  * KYC Verification
  * Newsletter

#### Super Admin Dashboard Layout

* **Component**: Uses the ModernSuperAdminDashboardLayout component
* **Users Section**: No banner, reduced bottom margins and padding
* **Businesses Section**: Follows same layout, design, and styling as Users section
* **Verifications Section**: Follows same layout, design, and styling as other sections
* **Listings Section**: Follows same layout, design, and styling as other sections

#### Implementation Notes

* All dashboard layouts should fully utilize available space without empty areas
* Section headers should have consistent styling without white backgrounds
* Success notification cards should match the enhanced dashboard banner (90% width, 16px border-radius)
* Breadcrumb navigation should match the width and alignment of dashboard elements

### 7.3. Business Cards

Business cards are used throughout the application to display business listings in a consistent format.

#### Business Card Design Elements

* **Business Owner Avatar**: Positioned on the right side of the card
* **Verification Icon**: Displayed next to business names for verified businesses
* **Facilities Section**: Shows only icons, at least six per row
* **Premium/Featured Indicators**: Visual indicators for premium and featured businesses
* **Call/Message/View Buttons**: Positioned below the description section
* **Star Rating**: Numerical value displayed beside the stars

#### Implementation

The business cards are created using the `createBusinessCard(business)` function, which takes a business object and generates the HTML structure for the card. This function uses a JavaScript template literal for readability and maintainability.

#### Usage Contexts

Business cards appear in multiple locations:
* Search results page
* Explore listings page
* Homepage (featured/trending businesses)
* User dashboard (saved places)

### 7.4. KYC/KYB Verification Process

The platform incorporates robust verification processes for both users (KYC) and businesses (KYB) to ensure trust and security.

#### Verification Workflow

1. **Submission**:
   * User/Business owner submits required documents through the dashboard
   * Documents are uploaded to Cloudinary with secure access controls
   * Verification status is set to "pending"

2. **Review**:
   * Admin/Super Admin reviews the submitted documents
   * Verifies the authenticity and completeness of documents

3. **Decision**:
   * Admin approves or rejects the submission
   * If rejected, a reason must be provided

4. **Notification**:
   * User/Business owner is notified of the decision
   * If approved, verification badge is displayed
   * If rejected, reason is displayed and option to resubmit is provided

#### Document Types

**KYC Documents**:
* ID front
* ID back
* Proof of address

**KYB Documents**:
* Business license
* Tax ID
* Proof of business address

#### Verification Badge

Once verified, a verification badge is displayed:
* Next to the user's name in reviews and messages
* Next to the business name on business listings and detail pages

## 8. UI Standards and Design Guidelines

### 8.1. Color Palette

* **Primary Color**: #359e04 (green)
  * Used for primary buttons, links, and accent elements
  * Used for highlighting sections in the sidebar
  * Used consistently across all dashboard sections

* **Background Colors**:
  * Home page: Green background (#359e04) for header
  * All other pages: White background for header and content areas

* **Button Colors**:
  * Primary actions: Green (#359e04)
  * Secondary actions: [Specify secondary button color]
  * Note: The application doesn't use blue for buttons, including the Add Listing button for non-signed-in users

### 8.2. Typography

* **Font Family**: [Specify the font family used]
* **Heading Sizes**: [Specify heading sizes]
* **Body Text**: [Specify body text size]

### 8.3. Component Styling

#### Cards

* **Dashboard Cards**:
  * Modern and sleek design
  * Consistent styling across all dashboard sections
  * Rounded corners (border-radius: 16px)
  * Shadow effects for depth

* **Business Cards**:
  * Beautiful, modern, and sleek design
  * Consistent layout with business owner avatar on right
  * Verification icon next to business name
  * Facilities section showing only icons (at least six per row)
  * Premium/featured business indicators
  * Call/message/view details buttons below description
  * Star rating value displayed beside stars

#### Notifications

* **Success Notification Card**:
  * 90% width
  * 16px border-radius
  * Centered with margin-left: auto and margin-right: auto
  * Matches enhanced dashboard banner styling

#### Navigation

* **Breadcrumb Navigation**:
  * 90% width
  * Centered with margin-left: auto and margin-right: auto
  * Matches dashboard banner and notification card styling

### 8.4. Layout Guidelines

#### Spacing

* **Dashboard**:
  * 70px top margin for main content
  * 10px top margin for dashboard banner
  * Reduced bottom margins and padding for search bars
  * Reduced bottom spacing/padding after content in UI sections

#### Width

* All sections in the dashboard use 90% width instead of 100% width

#### Height

* Content areas have flexible height based on actual content
* Avoid fixed or minimum height constraints that create excessive empty space

### 8.5. Business Details Page

* **Banner Section**:
  * Four images displayed instead of three
  * More visible 'See Photos' element

* **Navigation Tabs**:
  * Overview, Photos & Videos, and Reviews tabs
  * Sticky behavior when scrolling

### 8.6. Responsive Design

* **Breakpoints**: [Specify breakpoints for responsive design]
* **Mobile Considerations**: [Specify mobile-specific design guidelines]

## 9. Security Considerations

[Note: This section would contain detailed security considerations. For brevity, we're focusing on adding new documentation for key components.]

## 10. Testing Strategy

### 10.1. User Flow Testing

Testing should focus on complete user flows for all user roles:

* **Customer Flows**:
  * Searching for businesses with different filters
  * Viewing business details
  * Leaving reviews
  * Bookmarking businesses
  * Booking appointments/reservations/ordering food
  * Sending messages
  * Calling businesses
  * Submitting KYC documents

* **Business Owner Flows**:
  * Creating business listings
  * Editing business listings
  * Managing bookings
  * Upgrading to premium listings
  * Submitting KYB documents
  * Managing advertising campaigns

* **Admin/Super Admin Flows**:
  * Managing users and businesses
  * Reviewing KYC/KYB submissions
  * Managing newsletter campaigns
  * Configuring system settings

* **Content Manager Flows**:
  * Creating and publishing blog posts
  * Managing blog categories and tags

### 10.2. Component Testing

Individual components should be tested for functionality and appearance:

* **Header**: Test behavior on different pages (home vs. non-home)
* **Dashboard Layouts**: Test for proper spacing, alignment, and responsiveness
* **Business Cards**: Test display of all elements and proper styling
* **Forms**: Test validation, submission, and error handling

### 10.3. Integration Testing

Test the integration between different parts of the application:

* **Authentication**: Test login, registration, and authorization flows
* **Payment Processing**: Test successful and failed payment scenarios
* **File Uploads**: Test document and image uploads to Cloudinary
* **Email Notifications**: Test sending and receiving of transactional emails

## 11. Deployment

### 11.1. Requirements

* **A Next.js Hosting Provider**: Vercel (recommended for Next.js projects)
* **A MongoDB database**: MongoDB Atlas (recommended)
* **A Cloudinary account**: For storing and managing images
* **A payment gateway account**: Paystack/Flutterwave
* **An email service provider account**: Mailgun
* **A domain name**: For production deployment
* **Git**: For version control and code deployment
* **Node.js and npm**: For development and building

### 11.2. Environment Variables

The application requires several environment variables to be set:

```
MONGODB_URI=mongodb://localhost:27017/finda
JWT_SECRET=your-super-secret-jwt-key
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
PAYSTACK_SECRET_KEY=your-paystack-secret-key
MAILGUN_API_KEY=your-mailgun-api-key
BASE_URL=http://localhost:3000
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

**Important**: Production environment variables should be set on the hosting provider, not in a `.env` file.

### 11.3. Deployment Process

1. **Prepare the Application**:
   * Ensure all dependencies are up to date
   * Run tests to verify functionality
   * Build the application for production

2. **Deploy to Hosting Provider**:
   * Connect repository to hosting provider (e.g., Vercel)
   * Configure environment variables
   * Deploy the application

3. **Post-Deployment Verification**:
   * Verify all features work in the production environment
   * Monitor for any errors or performance issues

## 12. Appendix

### 12.1. Glossary of Terms

* **KYC**: Know Your Customer - Process of verifying the identity of users
* **KYB**: Know Your Business - Process of verifying the legitimacy of businesses
* **JWT**: JSON Web Token - Used for secure authentication
* **ODM**: Object Data Mapper - Mongoose is an ODM for MongoDB

### 12.2. API Documentation

[Note: This section would contain detailed API documentation]

### 12.3. Change Log

* **Version 1.0.0** (Initial Documentation)
* **Version 1.1.0** (Current Version):
  * Added Table of Contents
  * Updated Technology Stack section for clarity
  * Added Permissions Matrix to User Roles section
  * Added detailed documentation for Header Component
  * Added detailed documentation for Dashboard Layouts
  * Added detailed documentation for Business Cards
  * Added detailed documentation for KYC/KYB Verification Process
  * Added UI Standards and Design Guidelines section
  * Improved document structure and formatting
