# Finda - Find Everything In Your City

Finda is a comprehensive directory and listing platform that helps users find businesses, services, and places of interest in their city. It combines features from business directories like Yelp and Google Business with integrated booking capabilities, a blog for content marketing, and a newsletter for user engagement.

## Features

- **Multiple Listing Types**: Search for businesses, services, places, cars, real estate, events, and jobs
- **Detailed Listings**: View listings with photos, descriptions, contact information, operating hours, and more
- **User Authentication**: Secure JWT-based authentication with different user roles
- **User Profiles**: Manage your profile, bookmarks, and reviews
- **Business Management**: Add and manage your own business listings
- **Verification System**: KYC (Know Your Customer) and KYB (Know Your Business) verification processes
- **Booking System**: Make reservations and appointments with businesses
- **Messaging**: Direct communication between customers and businesses
- **Payment Processing**: Integrated payment gateways (Paystack/Flutterwave)
- **Premium Listings**: Enhanced visibility for businesses with premium subscriptions
- **Advertising**: Targeted advertising campaigns for businesses
- **Blog**: Content marketing with rich text editing capabilities
- **Newsletter**: User engagement through email newsletters
- **Reviews and Ratings**: Customer feedback and ratings for businesses
- **Wallet**: Manage payments and transactions
- **Mobile-Friendly**: Responsive design for all devices

## User Roles

- **Customer**: Search businesses, leave reviews, make bookings, and more
- **Business Owner**: Create and manage business listings, respond to reviews, manage bookings
- **Admin**: Manage users, businesses, and content across the platform
- **Super Admin**: Full access to all platform features and settings
- **Content Manager**: Create and manage blog content

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm or yarn
- MongoDB database
- Cloudinary account (for image uploads)
- Payment gateway account (Paystack/Flutterwave)
- Email service account (Mailgun)
- Google Maps API key

### Installation

1. Clone the repository

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up MongoDB:

   - Create a MongoDB database (local or MongoDB Atlas)
   - The application will automatically create the necessary collections

4. Set up environment variables:
   - Copy the `.env.example` file to `.env.local`
   - Update the values in `.env.local` as needed (see Environment Variables section)

5. Start the development server:

   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Environment Variables

The following environment variables can be configured in your `.env.local` file:

| Variable | Description | Default |
|----------|-------------|----------|
| NEXT_PUBLIC_APP_NAME | Application name | Finda |
| NEXT_PUBLIC_APP_DESCRIPTION | Application description | Find everything in your city |
| NEXT_PUBLIC_APP_URL | Application URL | `http://localhost:3000` |
| NEXT_PUBLIC_API_URL | API URL | `http://localhost:3000/api` |
| MONGODB_URI | MongoDB connection string | `mongodb://localhost:27017/finda` |
| JWT_SECRET | Secret key for JWT tokens | - |
| JWT_EXPIRES_IN | JWT token expiration time | 7d |
| CLOUDINARY_CLOUD_NAME | Cloudinary cloud name | - |
| CLOUDINARY_API_KEY | Cloudinary API key | - |
| CLOUDINARY_API_SECRET | Cloudinary API secret | - |
| PAYSTACK_SECRET_KEY | Paystack secret key | - |
| MAILGUN_API_KEY | Mailgun API key | - |
| MAILGUN_DOMAIN | Mailgun domain | - |
| NEXT_PUBLIC_GOOGLE_MAPS_API_KEY | Google Maps API Key | - |
| NEXT_PUBLIC_DEFAULT_COUNTRY | Default country | Nigeria |
| NEXT_PUBLIC_DEFAULT_CITY | Default city | Abuja |
| NEXT_PUBLIC_ENABLE_BLOG | Enable blog feature | true |
| NEXT_PUBLIC_ENABLE_NEWSLETTER | Enable newsletter feature | true |
| NEXT_PUBLIC_ENABLE_REVIEWS | Enable reviews feature | true |

## Project Structure

```text
finda/
├── public/                # Static assets
├── src/
│   ├── pages/             # Next.js pages (Pages Router)
│   │   ├── api/           # API routes
│   │   └── ...            # Page components
│   ├── components/        # React components
│   ├── models/            # Mongoose models
│   ├── services/          # API services
│   ├── middleware/        # Custom middleware
│   ├── styles/            # CSS styles
│   ├── utils/             # Utility functions
│   └── scripts/           # Utility scripts
├── .env.example           # Example environment variables
├── .env.local             # Local environment variables (not committed)
├── .gitignore             # Git ignore file
├── next.config.js         # Next.js configuration
├── package.json           # Project dependencies
├── DOCUMENT.md            # Detailed developer documentation
└── README.md              # Project documentation
```

## Testing

The project includes a comprehensive testing suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:coverage

# Run specific test suites
npm run test:models
npm run test:api
npm run test:lib
```

## Database Seeding

To populate the database with sample data for development:

```bash
npm run seed:database
```

## Deployment

### Vercel (Recommended)

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new) from the creators of Next.js:

1. Push your code to a Git repository (GitHub, GitLab, Bitbucket)
2. Import the project into Vercel
3. Configure environment variables in the Vercel dashboard
4. Deploy

### Other Hosting Options

For other hosting options, refer to the detailed deployment instructions in the DOCUMENT.md file.

## Learn More

For detailed developer documentation, refer to the DOCUMENT.md file in the project root.

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.
