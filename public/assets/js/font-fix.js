// This script ensures Inter font is properly loaded and applied
document.addEventListener('DOMContentLoaded', function() {
  // Check if fonts are loaded
  if (document.fonts && document.fonts.ready) {
    document.fonts.ready.then(function() {
      console.log('Inter font is loaded and ready');
      document.documentElement.classList.add('fonts-loaded');
    }).catch(function(error) {
      console.warn('Error loading Inter font:', error);
    });
  }

  // Apply font classes to elements that might be missing them
  const applyFontClasses = () => {
    // Apply to headings
    document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(el => {
      if (!el.style.fontFamily) {
        el.classList.add('font-sans');
      }
      // Reset any custom letter spacing
      el.style.letterSpacing = 'normal';
    });

    // Apply to text elements
    document.querySelectorAll('p, span, div, a, button, input, textarea, select, option').forEach(el => {
      if (!el.style.fontFamily) {
        el.classList.add('font-sans');
      }
      // Reset any custom letter spacing
      el.style.letterSpacing = 'normal';
    });

    // Apply to specific components that might need it
    document.querySelectorAll('.location-name, .section-title h2, .section-title h6, .hero-banner h1, .hero-banner p, .view-all-btn, .carousel-item h4').forEach(el => {
      el.classList.add('font-sans');
      // Reset any custom letter spacing
      el.style.letterSpacing = 'normal';
    });
  };

  // Run once on load
  applyFontClasses();

  // Run again after a short delay to catch dynamically loaded content
  setTimeout(applyFontClasses, 1000);
});
