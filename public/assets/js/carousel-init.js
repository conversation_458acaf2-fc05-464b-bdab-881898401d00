// Initialize Bootstrap carousels
document.addEventListener('DOMContentLoaded', function() {
  // Check if Bootstrap is loaded
  if (typeof bootstrap !== 'undefined') {
    // Find all carousel elements
    const carousels = document.querySelectorAll('.carousel');
    
    // Initialize each carousel
    carousels.forEach(function(carouselEl) {
      const carousel = new bootstrap.Carousel(carouselEl, {
        interval: 5000,
        wrap: true,
        touch: true
      });
      
      // Store the carousel instance on the element
      carouselEl._carousel = carousel;
    });
    
    // Add click handlers for custom carousel controls
    const prevButtons = document.querySelectorAll('.carousel-control-prev');
    const nextButtons = document.querySelectorAll('.carousel-control-next');
    
    prevButtons.forEach(function(button) {
      button.addEventListener('click', function() {
        const carouselEl = this.closest('.carousel');
        if (carouselEl && carouselEl._carousel) {
          carouselEl._carousel.prev();
        }
      });
    });
    
    nextButtons.forEach(function(button) {
      button.addEventListener('click', function() {
        const carouselEl = this.closest('.carousel');
        if (carouselEl && carouselEl._carousel) {
          carouselEl._carousel.next();
        }
      });
    });
    
    // Add click handlers for custom indicators
    const indicators = document.querySelectorAll('.carousel-indicator');
    
    indicators.forEach(function(indicator, index) {
      indicator.addEventListener('click', function() {
        const carouselContainer = this.closest('.location-carousel-container');
        if (carouselContainer) {
          const carouselEl = carouselContainer.querySelector('.carousel');
          if (carouselEl && carouselEl._carousel) {
            carouselEl._carousel.to(index);
          }
        }
      });
    });
  } else {
    console.warn('Bootstrap not loaded. Carousels will not be initialized.');
    
    // Try to load Bootstrap
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
    script.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
    script.crossOrigin = 'anonymous';
    document.head.appendChild(script);
    
    // Try to initialize carousels after Bootstrap is loaded
    script.onload = function() {
      setTimeout(function() {
        const carousels = document.querySelectorAll('.carousel');
        carousels.forEach(function(carouselEl) {
          new bootstrap.Carousel(carouselEl, {
            interval: 5000,
            wrap: true
          });
        });
      }, 500);
    };
  }
});
